# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter_windows_3.24.3-stable\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\OneDrive\\Documents\\b2c-app" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.76+76" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 76 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 76 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter_windows_3.24.3-stable\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\OneDrive\\Documents\\b2c-app"
  "FLUTTER_ROOT=C:\\flutter_windows_3.24.3-stable\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\OneDrive\\Documents\\b2c-app\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\OneDrive\\Documents\\b2c-app"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\OneDrive\\Documents\\b2c-app\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuMA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YmU2OThjNDhhNg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MTg4MTgwMDk0OQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjA="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\OneDrive\\Documents\\b2c-app\\.dart_tool\\package_config.json"
)
