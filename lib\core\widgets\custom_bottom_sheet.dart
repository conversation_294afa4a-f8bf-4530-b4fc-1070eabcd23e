import 'package:flutter/material.dart';

import '../../widgets/custom_text.dart';
import '../themes/color_schemes.dart';

void showConfirmationBottomSheet({
  required BuildContext context,
  required String title,
  required String message,
  List<Widget>? children,
  required void Function() onProceed,
  void Function()? onBack,
  String? backText,
  String proceedText = 'Confirm',
  void Function()? onDispose,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    useRootNavigator: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (_) {
      return SafeArea(
          child: ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.85,
        ),
        child: NotificationListener<UserScrollNotification>(
          onNotification: (notification) {
            return false;
          },
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: double.infinity,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  decoration: BoxDecoration(
                    color: AppColors.neutral100,
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: CustomText(
                          title,
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: AppColors.neutral800,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                        ),
                      ),
                      const SizedBox(width: 12),
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 4),
                          child: Icon(Icons.close, color: AppColors.neutral600),
                        ),
                      ),
                    ],
                  ),
                ),
                Flexible(
                  child: SizedBox(
                    width: double.infinity,
                    child: ColoredBox(
                        color: AppColors.neutral150,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const SizedBox(height: 24.0),
                            // Confirmation message
                            Text(
                              message,
                              style: TextStyle(
                                fontSize: 14.0,
                                fontWeight: FontWeight.w500,
                                color: AppColors.neutral500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            ...children ?? [],
                          ],
                        )),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.fromLTRB(16, 20, 16, 20),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.neutral800.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Visibility(
                        visible: backText != null,
                        child: Expanded(
                          child: OutlinedButton(
                            style: OutlinedButton.styleFrom(
                              backgroundColor: AppColors.neutral100,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                              side: BorderSide(
                                color: AppColors.primary,
                                width: 1.5,
                              ),
                              minimumSize: const Size(double.infinity, 48),
                            ),
                            onPressed: onBack ?? () => Navigator.pop(context),
                            child: Text(
                              backText ?? '',
                              style: TextStyle(
                                fontSize: 16,
                                color: AppColors.primary,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Visibility(
                          visible: backText != null,
                          child: SizedBox(width: 20)),
                      Expanded(
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary500,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            minimumSize: const Size(double.infinity, 48),
                          ),
                          onPressed: onProceed,
                          child: CustomText(
                            proceedText,
                            fontSize: 16,
                            color: AppColors.neutral100,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: MediaQuery.of(context).viewInsets.bottom,
                )
              ],
            ),
          ),
        ),
      ));
    },
  ).then((_) {
    onDispose?.call();
  });
}
