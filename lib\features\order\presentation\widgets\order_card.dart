import 'package:flutter/material.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/app_card.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'order_status_chip.dart';
import '../../../../core/utils/helpers.dart';

class OrderCard extends StatelessWidget {
  final OrderEntity order;
  final VoidCallback? onTap;
  final VoidCallback? onReorder;
  final VoidCallback? onCancel;

  const OrderCard({
    super.key,
    required this.order,
    this.onTap,
    this.onReorder,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      backgroundColor: AppColors.neutral100,
      borderRadius: 12,
      elevation: 2,
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with order ID and status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: CustomText(
                  'Order #${order.id}',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.neutral600,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              OrderStatusChip(status: order.status),
            ],
          ),
          const SizedBox(height: 12),

          // Order date and estimated delivery
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: AppColors.neutral400,
              ),
              const SizedBox(width: 4),
              CustomText(
                order.formattedOrderDate,
                fontSize: 14,
                color: AppColors.neutral400,
              ),
              if (order.estimatedDeliveryTime != null) ...[
                const SizedBox(width: 16),
                Icon(
                  Icons.local_shipping,
                  size: 16,
                  color: AppColors.neutral400,
                ),
                const SizedBox(width: 4),
                CustomText(
                  formatDeliveryDate(order.estimatedDeliveryTime!),
                  fontSize: 14,
                  color: AppColors.neutral400,
                ),
              ],
            ],
          ),
          const SizedBox(height: 12),

          // Order summary
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              CustomText(
                '₹${order.totalAmount.toStringAsFixed(2)}',
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.neutral600,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // ignore: unused_element
  Widget _buildItemsPreview() {
    final displayItems = order.items.take(3).toList();

    return Row(
      children: [
        // Item images
        ...displayItems.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;

          return Container(
            margin:
                EdgeInsets.only(right: index < displayItems.length - 1 ? 8 : 0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: CustomImage(
                imageUrl:
                    item.imageUrl ?? 'assets/images/image-placeholder.jpg',
                width: 40,
                height: 40,
                fit: BoxFit.cover,
              ),
            ),
          );
        }),

        // Show more indicator if there are more items
        if (order.items.length > 3) ...[
          const SizedBox(width: 8),
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.neutral100,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Center(
              child: CustomText(
                '+${order.items.length - 3}',
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppColors.neutral400,
              ),
            ),
          ),
        ],

        const SizedBox(width: 12),

        // Item names
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                displayItems.map((item) => item.name).join(', '),
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.neutral600,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              if (order.items.length > 3)
                CustomText(
                  'and ${order.items.length - 3} more items',
                  fontSize: 12,
                  color: AppColors.neutral400,
                ),
            ],
          ),
        ),
      ],
    );
  }
}
