import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../data/models/adress_model.dart';

part 'location_state.freezed.dart';

@freezed
class LocationState with _$LocationState {
  const factory LocationState.initial() = _Initial;
  const factory LocationState.loading() = _Loading;
  const factory LocationState.loaded({
    required AddressModel address,
    @Default('') String durationText, // e.g. "15 mins". Optional, defaults to empty
  }) = _Loaded;
  const factory LocationState.error(String message) = _Error;
  const factory LocationState.notServiceable(AddressModel address) = _NotServiceable;
  // Special state for web platforms to initialize without blocking on location permission
  const factory LocationState.webInitial() = _WebInitial;
}
