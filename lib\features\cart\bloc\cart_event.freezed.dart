// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'cart_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CartEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CartEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'CartEvent()';
  }
}

/// @nodoc
class $CartEventCopyWith<$Res> {
  $CartEventCopyWith(CartEvent _, $Res Function(CartEvent) __);
}

/// Adds pattern-matching-related methods to [CartEvent].
extension CartEventPatterns on CartEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartInit value)? init,
    TResult Function(CartAddItem value)? addItem,
    TResult Function(CartRemoveItem value)? removeItem,
    TResult Function(CartUpdateQuantity value)? updateQuantity,
    TResult Function(CartClear value)? clear,
    TResult Function(CartApplyCoupon value)? applyCoupon,
    TResult Function(CartRemoveCoupon value)? removeCoupon,
    TResult Function(CartImportData value)? importData,
    TResult Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult Function(CartSelectAddress value)? selectAddress,
    TResult Function(CartClearAddress value)? clearAddress,
    TResult Function(CartPlaceOrder value)? placeOrder,
    TResult Function(CartCompleteOrder value)? completeOrder,
    TResult Function(CartPaymentFailed value)? paymentFailed,
    TResult Function(CartVerifyPayment value)? verifyPayment,
    TResult Function(CartCheckAvailability value)? checkAvailability,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case CartInit() when init != null:
        return init(_that);
      case CartAddItem() when addItem != null:
        return addItem(_that);
      case CartRemoveItem() when removeItem != null:
        return removeItem(_that);
      case CartUpdateQuantity() when updateQuantity != null:
        return updateQuantity(_that);
      case CartClear() when clear != null:
        return clear(_that);
      case CartApplyCoupon() when applyCoupon != null:
        return applyCoupon(_that);
      case CartRemoveCoupon() when removeCoupon != null:
        return removeCoupon(_that);
      case CartImportData() when importData != null:
        return importData(_that);
      case CartLoadDefaultAddress() when loadDefaultAddress != null:
        return loadDefaultAddress(_that);
      case CartSelectAddress() when selectAddress != null:
        return selectAddress(_that);
      case CartClearAddress() when clearAddress != null:
        return clearAddress(_that);
      case CartPlaceOrder() when placeOrder != null:
        return placeOrder(_that);
      case CartCompleteOrder() when completeOrder != null:
        return completeOrder(_that);
      case CartPaymentFailed() when paymentFailed != null:
        return paymentFailed(_that);
      case CartVerifyPayment() when verifyPayment != null:
        return verifyPayment(_that);
      case CartCheckAvailability() when checkAvailability != null:
        return checkAvailability(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartInit value) init,
    required TResult Function(CartAddItem value) addItem,
    required TResult Function(CartRemoveItem value) removeItem,
    required TResult Function(CartUpdateQuantity value) updateQuantity,
    required TResult Function(CartClear value) clear,
    required TResult Function(CartApplyCoupon value) applyCoupon,
    required TResult Function(CartRemoveCoupon value) removeCoupon,
    required TResult Function(CartImportData value) importData,
    required TResult Function(CartLoadDefaultAddress value) loadDefaultAddress,
    required TResult Function(CartSelectAddress value) selectAddress,
    required TResult Function(CartClearAddress value) clearAddress,
    required TResult Function(CartPlaceOrder value) placeOrder,
    required TResult Function(CartCompleteOrder value) completeOrder,
    required TResult Function(CartPaymentFailed value) paymentFailed,
    required TResult Function(CartVerifyPayment value) verifyPayment,
    required TResult Function(CartCheckAvailability value) checkAvailability,
  }) {
    final _that = this;
    switch (_that) {
      case CartInit():
        return init(_that);
      case CartAddItem():
        return addItem(_that);
      case CartRemoveItem():
        return removeItem(_that);
      case CartUpdateQuantity():
        return updateQuantity(_that);
      case CartClear():
        return clear(_that);
      case CartApplyCoupon():
        return applyCoupon(_that);
      case CartRemoveCoupon():
        return removeCoupon(_that);
      case CartImportData():
        return importData(_that);
      case CartLoadDefaultAddress():
        return loadDefaultAddress(_that);
      case CartSelectAddress():
        return selectAddress(_that);
      case CartClearAddress():
        return clearAddress(_that);
      case CartPlaceOrder():
        return placeOrder(_that);
      case CartCompleteOrder():
        return completeOrder(_that);
      case CartPaymentFailed():
        return paymentFailed(_that);
      case CartVerifyPayment():
        return verifyPayment(_that);
      case CartCheckAvailability():
        return checkAvailability(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartInit value)? init,
    TResult? Function(CartAddItem value)? addItem,
    TResult? Function(CartRemoveItem value)? removeItem,
    TResult? Function(CartUpdateQuantity value)? updateQuantity,
    TResult? Function(CartClear value)? clear,
    TResult? Function(CartApplyCoupon value)? applyCoupon,
    TResult? Function(CartRemoveCoupon value)? removeCoupon,
    TResult? Function(CartImportData value)? importData,
    TResult? Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult? Function(CartSelectAddress value)? selectAddress,
    TResult? Function(CartClearAddress value)? clearAddress,
    TResult? Function(CartPlaceOrder value)? placeOrder,
    TResult? Function(CartCompleteOrder value)? completeOrder,
    TResult? Function(CartPaymentFailed value)? paymentFailed,
    TResult? Function(CartVerifyPayment value)? verifyPayment,
    TResult? Function(CartCheckAvailability value)? checkAvailability,
  }) {
    final _that = this;
    switch (_that) {
      case CartInit() when init != null:
        return init(_that);
      case CartAddItem() when addItem != null:
        return addItem(_that);
      case CartRemoveItem() when removeItem != null:
        return removeItem(_that);
      case CartUpdateQuantity() when updateQuantity != null:
        return updateQuantity(_that);
      case CartClear() when clear != null:
        return clear(_that);
      case CartApplyCoupon() when applyCoupon != null:
        return applyCoupon(_that);
      case CartRemoveCoupon() when removeCoupon != null:
        return removeCoupon(_that);
      case CartImportData() when importData != null:
        return importData(_that);
      case CartLoadDefaultAddress() when loadDefaultAddress != null:
        return loadDefaultAddress(_that);
      case CartSelectAddress() when selectAddress != null:
        return selectAddress(_that);
      case CartClearAddress() when clearAddress != null:
        return clearAddress(_that);
      case CartPlaceOrder() when placeOrder != null:
        return placeOrder(_that);
      case CartCompleteOrder() when completeOrder != null:
        return completeOrder(_that);
      case CartPaymentFailed() when paymentFailed != null:
        return paymentFailed(_that);
      case CartVerifyPayment() when verifyPayment != null:
        return verifyPayment(_that);
      case CartCheckAvailability() when checkAvailability != null:
        return checkAvailability(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(CartItemModel item, String screen)? addItem,
    TResult Function(List<String>? itemIds, List<String>? skuIds)? removeItem,
    TResult Function(String itemId, String sku, int quantity, String screen)?
        updateQuantity,
    TResult Function()? clear,
    TResult Function(String code)? applyCoupon,
    TResult Function()? removeCoupon,
    TResult Function(String jsonData)? importData,
    TResult Function()? loadDefaultAddress,
    TResult Function(AddressModel address)? selectAddress,
    TResult Function()? clearAddress,
    TResult Function(String paymentMethod, List<ProductEntity>? freeBie)?
        placeOrder,
    TResult Function(String orderId, Map<String, dynamic> orderData)?
        completeOrder,
    TResult Function(String errorMessage)? paymentFailed,
    TResult Function(
            String razorpayOrderId,
            String razorpayPaymentId,
            String razorpaySignature,
            String orderId,
            Map<String, dynamic> orderData)?
        verifyPayment,
    TResult Function(List<String>? skus)? checkAvailability,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case CartInit() when init != null:
        return init();
      case CartAddItem() when addItem != null:
        return addItem(_that.item, _that.screen);
      case CartRemoveItem() when removeItem != null:
        return removeItem(_that.itemIds, _that.skuIds);
      case CartUpdateQuantity() when updateQuantity != null:
        return updateQuantity(
            _that.itemId, _that.sku, _that.quantity, _that.screen);
      case CartClear() when clear != null:
        return clear();
      case CartApplyCoupon() when applyCoupon != null:
        return applyCoupon(_that.code);
      case CartRemoveCoupon() when removeCoupon != null:
        return removeCoupon();
      case CartImportData() when importData != null:
        return importData(_that.jsonData);
      case CartLoadDefaultAddress() when loadDefaultAddress != null:
        return loadDefaultAddress();
      case CartSelectAddress() when selectAddress != null:
        return selectAddress(_that.address);
      case CartClearAddress() when clearAddress != null:
        return clearAddress();
      case CartPlaceOrder() when placeOrder != null:
        return placeOrder(_that.paymentMethod, _that.freeBie);
      case CartCompleteOrder() when completeOrder != null:
        return completeOrder(_that.orderId, _that.orderData);
      case CartPaymentFailed() when paymentFailed != null:
        return paymentFailed(_that.errorMessage);
      case CartVerifyPayment() when verifyPayment != null:
        return verifyPayment(_that.razorpayOrderId, _that.razorpayPaymentId,
            _that.razorpaySignature, _that.orderId, _that.orderData);
      case CartCheckAvailability() when checkAvailability != null:
        return checkAvailability(_that.skus);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(CartItemModel item, String screen) addItem,
    required TResult Function(List<String>? itemIds, List<String>? skuIds)
        removeItem,
    required TResult Function(
            String itemId, String sku, int quantity, String screen)
        updateQuantity,
    required TResult Function() clear,
    required TResult Function(String code) applyCoupon,
    required TResult Function() removeCoupon,
    required TResult Function(String jsonData) importData,
    required TResult Function() loadDefaultAddress,
    required TResult Function(AddressModel address) selectAddress,
    required TResult Function() clearAddress,
    required TResult Function(
            String paymentMethod, List<ProductEntity>? freeBie)
        placeOrder,
    required TResult Function(String orderId, Map<String, dynamic> orderData)
        completeOrder,
    required TResult Function(String errorMessage) paymentFailed,
    required TResult Function(
            String razorpayOrderId,
            String razorpayPaymentId,
            String razorpaySignature,
            String orderId,
            Map<String, dynamic> orderData)
        verifyPayment,
    required TResult Function(List<String>? skus) checkAvailability,
  }) {
    final _that = this;
    switch (_that) {
      case CartInit():
        return init();
      case CartAddItem():
        return addItem(_that.item, _that.screen);
      case CartRemoveItem():
        return removeItem(_that.itemIds, _that.skuIds);
      case CartUpdateQuantity():
        return updateQuantity(
            _that.itemId, _that.sku, _that.quantity, _that.screen);
      case CartClear():
        return clear();
      case CartApplyCoupon():
        return applyCoupon(_that.code);
      case CartRemoveCoupon():
        return removeCoupon();
      case CartImportData():
        return importData(_that.jsonData);
      case CartLoadDefaultAddress():
        return loadDefaultAddress();
      case CartSelectAddress():
        return selectAddress(_that.address);
      case CartClearAddress():
        return clearAddress();
      case CartPlaceOrder():
        return placeOrder(_that.paymentMethod, _that.freeBie);
      case CartCompleteOrder():
        return completeOrder(_that.orderId, _that.orderData);
      case CartPaymentFailed():
        return paymentFailed(_that.errorMessage);
      case CartVerifyPayment():
        return verifyPayment(_that.razorpayOrderId, _that.razorpayPaymentId,
            _that.razorpaySignature, _that.orderId, _that.orderData);
      case CartCheckAvailability():
        return checkAvailability(_that.skus);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(CartItemModel item, String screen)? addItem,
    TResult? Function(List<String>? itemIds, List<String>? skuIds)? removeItem,
    TResult? Function(String itemId, String sku, int quantity, String screen)?
        updateQuantity,
    TResult? Function()? clear,
    TResult? Function(String code)? applyCoupon,
    TResult? Function()? removeCoupon,
    TResult? Function(String jsonData)? importData,
    TResult? Function()? loadDefaultAddress,
    TResult? Function(AddressModel address)? selectAddress,
    TResult? Function()? clearAddress,
    TResult? Function(String paymentMethod, List<ProductEntity>? freeBie)?
        placeOrder,
    TResult? Function(String orderId, Map<String, dynamic> orderData)?
        completeOrder,
    TResult? Function(String errorMessage)? paymentFailed,
    TResult? Function(
            String razorpayOrderId,
            String razorpayPaymentId,
            String razorpaySignature,
            String orderId,
            Map<String, dynamic> orderData)?
        verifyPayment,
    TResult? Function(List<String>? skus)? checkAvailability,
  }) {
    final _that = this;
    switch (_that) {
      case CartInit() when init != null:
        return init();
      case CartAddItem() when addItem != null:
        return addItem(_that.item, _that.screen);
      case CartRemoveItem() when removeItem != null:
        return removeItem(_that.itemIds, _that.skuIds);
      case CartUpdateQuantity() when updateQuantity != null:
        return updateQuantity(
            _that.itemId, _that.sku, _that.quantity, _that.screen);
      case CartClear() when clear != null:
        return clear();
      case CartApplyCoupon() when applyCoupon != null:
        return applyCoupon(_that.code);
      case CartRemoveCoupon() when removeCoupon != null:
        return removeCoupon();
      case CartImportData() when importData != null:
        return importData(_that.jsonData);
      case CartLoadDefaultAddress() when loadDefaultAddress != null:
        return loadDefaultAddress();
      case CartSelectAddress() when selectAddress != null:
        return selectAddress(_that.address);
      case CartClearAddress() when clearAddress != null:
        return clearAddress();
      case CartPlaceOrder() when placeOrder != null:
        return placeOrder(_that.paymentMethod, _that.freeBie);
      case CartCompleteOrder() when completeOrder != null:
        return completeOrder(_that.orderId, _that.orderData);
      case CartPaymentFailed() when paymentFailed != null:
        return paymentFailed(_that.errorMessage);
      case CartVerifyPayment() when verifyPayment != null:
        return verifyPayment(_that.razorpayOrderId, _that.razorpayPaymentId,
            _that.razorpaySignature, _that.orderId, _that.orderData);
      case CartCheckAvailability() when checkAvailability != null:
        return checkAvailability(_that.skus);
      case _:
        return null;
    }
  }
}

/// @nodoc

class CartInit implements CartEvent {
  const CartInit();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CartInit);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'CartEvent.init()';
  }
}

/// @nodoc

class CartAddItem implements CartEvent {
  const CartAddItem({required this.item, this.screen = ''});

  final CartItemModel item;
  @JsonKey()
  final String screen;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CartAddItemCopyWith<CartAddItem> get copyWith =>
      _$CartAddItemCopyWithImpl<CartAddItem>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CartAddItem &&
            (identical(other.item, item) || other.item == item) &&
            (identical(other.screen, screen) || other.screen == screen));
  }

  @override
  int get hashCode => Object.hash(runtimeType, item, screen);

  @override
  String toString() {
    return 'CartEvent.addItem(item: $item, screen: $screen)';
  }
}

/// @nodoc
abstract mixin class $CartAddItemCopyWith<$Res>
    implements $CartEventCopyWith<$Res> {
  factory $CartAddItemCopyWith(
          CartAddItem value, $Res Function(CartAddItem) _then) =
      _$CartAddItemCopyWithImpl;
  @useResult
  $Res call({CartItemModel item, String screen});
}

/// @nodoc
class _$CartAddItemCopyWithImpl<$Res> implements $CartAddItemCopyWith<$Res> {
  _$CartAddItemCopyWithImpl(this._self, this._then);

  final CartAddItem _self;
  final $Res Function(CartAddItem) _then;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? item = null,
    Object? screen = null,
  }) {
    return _then(CartAddItem(
      item: null == item
          ? _self.item
          : item // ignore: cast_nullable_to_non_nullable
              as CartItemModel,
      screen: null == screen
          ? _self.screen
          : screen // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class CartRemoveItem implements CartEvent {
  const CartRemoveItem(
      {final List<String>? itemIds, final List<String>? skuIds})
      : _itemIds = itemIds,
        _skuIds = skuIds;

  final List<String>? _itemIds;
  List<String>? get itemIds {
    final value = _itemIds;
    if (value == null) return null;
    if (_itemIds is EqualUnmodifiableListView) return _itemIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Now a list of item IDs
  final List<String>? _skuIds;
// Now a list of item IDs
  List<String>? get skuIds {
    final value = _skuIds;
    if (value == null) return null;
    if (_skuIds is EqualUnmodifiableListView) return _skuIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CartRemoveItemCopyWith<CartRemoveItem> get copyWith =>
      _$CartRemoveItemCopyWithImpl<CartRemoveItem>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CartRemoveItem &&
            const DeepCollectionEquality().equals(other._itemIds, _itemIds) &&
            const DeepCollectionEquality().equals(other._skuIds, _skuIds));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_itemIds),
      const DeepCollectionEquality().hash(_skuIds));

  @override
  String toString() {
    return 'CartEvent.removeItem(itemIds: $itemIds, skuIds: $skuIds)';
  }
}

/// @nodoc
abstract mixin class $CartRemoveItemCopyWith<$Res>
    implements $CartEventCopyWith<$Res> {
  factory $CartRemoveItemCopyWith(
          CartRemoveItem value, $Res Function(CartRemoveItem) _then) =
      _$CartRemoveItemCopyWithImpl;
  @useResult
  $Res call({List<String>? itemIds, List<String>? skuIds});
}

/// @nodoc
class _$CartRemoveItemCopyWithImpl<$Res>
    implements $CartRemoveItemCopyWith<$Res> {
  _$CartRemoveItemCopyWithImpl(this._self, this._then);

  final CartRemoveItem _self;
  final $Res Function(CartRemoveItem) _then;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? itemIds = freezed,
    Object? skuIds = freezed,
  }) {
    return _then(CartRemoveItem(
      itemIds: freezed == itemIds
          ? _self._itemIds
          : itemIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      skuIds: freezed == skuIds
          ? _self._skuIds
          : skuIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc

class CartUpdateQuantity implements CartEvent {
  const CartUpdateQuantity(this.itemId, this.sku, this.quantity,
      {this.screen = ''});

  final String itemId;
  final String sku;
  final int quantity;
  @JsonKey()
  final String screen;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CartUpdateQuantityCopyWith<CartUpdateQuantity> get copyWith =>
      _$CartUpdateQuantityCopyWithImpl<CartUpdateQuantity>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CartUpdateQuantity &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.sku, sku) || other.sku == sku) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.screen, screen) || other.screen == screen));
  }

  @override
  int get hashCode => Object.hash(runtimeType, itemId, sku, quantity, screen);

  @override
  String toString() {
    return 'CartEvent.updateQuantity(itemId: $itemId, sku: $sku, quantity: $quantity, screen: $screen)';
  }
}

/// @nodoc
abstract mixin class $CartUpdateQuantityCopyWith<$Res>
    implements $CartEventCopyWith<$Res> {
  factory $CartUpdateQuantityCopyWith(
          CartUpdateQuantity value, $Res Function(CartUpdateQuantity) _then) =
      _$CartUpdateQuantityCopyWithImpl;
  @useResult
  $Res call({String itemId, String sku, int quantity, String screen});
}

/// @nodoc
class _$CartUpdateQuantityCopyWithImpl<$Res>
    implements $CartUpdateQuantityCopyWith<$Res> {
  _$CartUpdateQuantityCopyWithImpl(this._self, this._then);

  final CartUpdateQuantity _self;
  final $Res Function(CartUpdateQuantity) _then;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? itemId = null,
    Object? sku = null,
    Object? quantity = null,
    Object? screen = null,
  }) {
    return _then(CartUpdateQuantity(
      null == itemId
          ? _self.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      null == sku
          ? _self.sku
          : sku // ignore: cast_nullable_to_non_nullable
              as String,
      null == quantity
          ? _self.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      screen: null == screen
          ? _self.screen
          : screen // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class CartClear implements CartEvent {
  const CartClear();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CartClear);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'CartEvent.clear()';
  }
}

/// @nodoc

class CartApplyCoupon implements CartEvent {
  const CartApplyCoupon(this.code);

  final String code;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CartApplyCouponCopyWith<CartApplyCoupon> get copyWith =>
      _$CartApplyCouponCopyWithImpl<CartApplyCoupon>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CartApplyCoupon &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, code);

  @override
  String toString() {
    return 'CartEvent.applyCoupon(code: $code)';
  }
}

/// @nodoc
abstract mixin class $CartApplyCouponCopyWith<$Res>
    implements $CartEventCopyWith<$Res> {
  factory $CartApplyCouponCopyWith(
          CartApplyCoupon value, $Res Function(CartApplyCoupon) _then) =
      _$CartApplyCouponCopyWithImpl;
  @useResult
  $Res call({String code});
}

/// @nodoc
class _$CartApplyCouponCopyWithImpl<$Res>
    implements $CartApplyCouponCopyWith<$Res> {
  _$CartApplyCouponCopyWithImpl(this._self, this._then);

  final CartApplyCoupon _self;
  final $Res Function(CartApplyCoupon) _then;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? code = null,
  }) {
    return _then(CartApplyCoupon(
      null == code
          ? _self.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class CartRemoveCoupon implements CartEvent {
  const CartRemoveCoupon();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CartRemoveCoupon);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'CartEvent.removeCoupon()';
  }
}

/// @nodoc

class CartImportData implements CartEvent {
  const CartImportData(this.jsonData);

  final String jsonData;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CartImportDataCopyWith<CartImportData> get copyWith =>
      _$CartImportDataCopyWithImpl<CartImportData>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CartImportData &&
            (identical(other.jsonData, jsonData) ||
                other.jsonData == jsonData));
  }

  @override
  int get hashCode => Object.hash(runtimeType, jsonData);

  @override
  String toString() {
    return 'CartEvent.importData(jsonData: $jsonData)';
  }
}

/// @nodoc
abstract mixin class $CartImportDataCopyWith<$Res>
    implements $CartEventCopyWith<$Res> {
  factory $CartImportDataCopyWith(
          CartImportData value, $Res Function(CartImportData) _then) =
      _$CartImportDataCopyWithImpl;
  @useResult
  $Res call({String jsonData});
}

/// @nodoc
class _$CartImportDataCopyWithImpl<$Res>
    implements $CartImportDataCopyWith<$Res> {
  _$CartImportDataCopyWithImpl(this._self, this._then);

  final CartImportData _self;
  final $Res Function(CartImportData) _then;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? jsonData = null,
  }) {
    return _then(CartImportData(
      null == jsonData
          ? _self.jsonData
          : jsonData // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class CartLoadDefaultAddress implements CartEvent {
  const CartLoadDefaultAddress();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CartLoadDefaultAddress);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'CartEvent.loadDefaultAddress()';
  }
}

/// @nodoc

class CartSelectAddress implements CartEvent {
  const CartSelectAddress(this.address);

  final AddressModel address;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CartSelectAddressCopyWith<CartSelectAddress> get copyWith =>
      _$CartSelectAddressCopyWithImpl<CartSelectAddress>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CartSelectAddress &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, address);

  @override
  String toString() {
    return 'CartEvent.selectAddress(address: $address)';
  }
}

/// @nodoc
abstract mixin class $CartSelectAddressCopyWith<$Res>
    implements $CartEventCopyWith<$Res> {
  factory $CartSelectAddressCopyWith(
          CartSelectAddress value, $Res Function(CartSelectAddress) _then) =
      _$CartSelectAddressCopyWithImpl;
  @useResult
  $Res call({AddressModel address});
}

/// @nodoc
class _$CartSelectAddressCopyWithImpl<$Res>
    implements $CartSelectAddressCopyWith<$Res> {
  _$CartSelectAddressCopyWithImpl(this._self, this._then);

  final CartSelectAddress _self;
  final $Res Function(CartSelectAddress) _then;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? address = null,
  }) {
    return _then(CartSelectAddress(
      null == address
          ? _self.address
          : address // ignore: cast_nullable_to_non_nullable
              as AddressModel,
    ));
  }
}

/// @nodoc

class CartClearAddress implements CartEvent {
  const CartClearAddress();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CartClearAddress);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'CartEvent.clearAddress()';
  }
}

/// @nodoc

class CartPlaceOrder implements CartEvent {
  const CartPlaceOrder(
      {required this.paymentMethod, final List<ProductEntity>? freeBie})
      : _freeBie = freeBie;

  final String paymentMethod;
  final List<ProductEntity>? _freeBie;
  List<ProductEntity>? get freeBie {
    final value = _freeBie;
    if (value == null) return null;
    if (_freeBie is EqualUnmodifiableListView) return _freeBie;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CartPlaceOrderCopyWith<CartPlaceOrder> get copyWith =>
      _$CartPlaceOrderCopyWithImpl<CartPlaceOrder>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CartPlaceOrder &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            const DeepCollectionEquality().equals(other._freeBie, _freeBie));
  }

  @override
  int get hashCode => Object.hash(runtimeType, paymentMethod,
      const DeepCollectionEquality().hash(_freeBie));

  @override
  String toString() {
    return 'CartEvent.placeOrder(paymentMethod: $paymentMethod, freeBie: $freeBie)';
  }
}

/// @nodoc
abstract mixin class $CartPlaceOrderCopyWith<$Res>
    implements $CartEventCopyWith<$Res> {
  factory $CartPlaceOrderCopyWith(
          CartPlaceOrder value, $Res Function(CartPlaceOrder) _then) =
      _$CartPlaceOrderCopyWithImpl;
  @useResult
  $Res call({String paymentMethod, List<ProductEntity>? freeBie});
}

/// @nodoc
class _$CartPlaceOrderCopyWithImpl<$Res>
    implements $CartPlaceOrderCopyWith<$Res> {
  _$CartPlaceOrderCopyWithImpl(this._self, this._then);

  final CartPlaceOrder _self;
  final $Res Function(CartPlaceOrder) _then;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? paymentMethod = null,
    Object? freeBie = freezed,
  }) {
    return _then(CartPlaceOrder(
      paymentMethod: null == paymentMethod
          ? _self.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String,
      freeBie: freezed == freeBie
          ? _self._freeBie
          : freeBie // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>?,
    ));
  }
}

/// @nodoc

class CartCompleteOrder implements CartEvent {
  const CartCompleteOrder(
      {required this.orderId, required final Map<String, dynamic> orderData})
      : _orderData = orderData;

  final String orderId;
  final Map<String, dynamic> _orderData;
  Map<String, dynamic> get orderData {
    if (_orderData is EqualUnmodifiableMapView) return _orderData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_orderData);
  }

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CartCompleteOrderCopyWith<CartCompleteOrder> get copyWith =>
      _$CartCompleteOrderCopyWithImpl<CartCompleteOrder>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CartCompleteOrder &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            const DeepCollectionEquality()
                .equals(other._orderData, _orderData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, orderId, const DeepCollectionEquality().hash(_orderData));

  @override
  String toString() {
    return 'CartEvent.completeOrder(orderId: $orderId, orderData: $orderData)';
  }
}

/// @nodoc
abstract mixin class $CartCompleteOrderCopyWith<$Res>
    implements $CartEventCopyWith<$Res> {
  factory $CartCompleteOrderCopyWith(
          CartCompleteOrder value, $Res Function(CartCompleteOrder) _then) =
      _$CartCompleteOrderCopyWithImpl;
  @useResult
  $Res call({String orderId, Map<String, dynamic> orderData});
}

/// @nodoc
class _$CartCompleteOrderCopyWithImpl<$Res>
    implements $CartCompleteOrderCopyWith<$Res> {
  _$CartCompleteOrderCopyWithImpl(this._self, this._then);

  final CartCompleteOrder _self;
  final $Res Function(CartCompleteOrder) _then;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? orderId = null,
    Object? orderData = null,
  }) {
    return _then(CartCompleteOrder(
      orderId: null == orderId
          ? _self.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
      orderData: null == orderData
          ? _self._orderData
          : orderData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class CartPaymentFailed implements CartEvent {
  const CartPaymentFailed(this.errorMessage);

  final String errorMessage;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CartPaymentFailedCopyWith<CartPaymentFailed> get copyWith =>
      _$CartPaymentFailedCopyWithImpl<CartPaymentFailed>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CartPaymentFailed &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, errorMessage);

  @override
  String toString() {
    return 'CartEvent.paymentFailed(errorMessage: $errorMessage)';
  }
}

/// @nodoc
abstract mixin class $CartPaymentFailedCopyWith<$Res>
    implements $CartEventCopyWith<$Res> {
  factory $CartPaymentFailedCopyWith(
          CartPaymentFailed value, $Res Function(CartPaymentFailed) _then) =
      _$CartPaymentFailedCopyWithImpl;
  @useResult
  $Res call({String errorMessage});
}

/// @nodoc
class _$CartPaymentFailedCopyWithImpl<$Res>
    implements $CartPaymentFailedCopyWith<$Res> {
  _$CartPaymentFailedCopyWithImpl(this._self, this._then);

  final CartPaymentFailed _self;
  final $Res Function(CartPaymentFailed) _then;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? errorMessage = null,
  }) {
    return _then(CartPaymentFailed(
      null == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class CartVerifyPayment implements CartEvent {
  const CartVerifyPayment(
      {required this.razorpayOrderId,
      required this.razorpayPaymentId,
      required this.razorpaySignature,
      required this.orderId,
      required final Map<String, dynamic> orderData})
      : _orderData = orderData;

  final String razorpayOrderId;
  final String razorpayPaymentId;
  final String razorpaySignature;
  final String orderId;
  final Map<String, dynamic> _orderData;
  Map<String, dynamic> get orderData {
    if (_orderData is EqualUnmodifiableMapView) return _orderData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_orderData);
  }

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CartVerifyPaymentCopyWith<CartVerifyPayment> get copyWith =>
      _$CartVerifyPaymentCopyWithImpl<CartVerifyPayment>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CartVerifyPayment &&
            (identical(other.razorpayOrderId, razorpayOrderId) ||
                other.razorpayOrderId == razorpayOrderId) &&
            (identical(other.razorpayPaymentId, razorpayPaymentId) ||
                other.razorpayPaymentId == razorpayPaymentId) &&
            (identical(other.razorpaySignature, razorpaySignature) ||
                other.razorpaySignature == razorpaySignature) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            const DeepCollectionEquality()
                .equals(other._orderData, _orderData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      razorpayOrderId,
      razorpayPaymentId,
      razorpaySignature,
      orderId,
      const DeepCollectionEquality().hash(_orderData));

  @override
  String toString() {
    return 'CartEvent.verifyPayment(razorpayOrderId: $razorpayOrderId, razorpayPaymentId: $razorpayPaymentId, razorpaySignature: $razorpaySignature, orderId: $orderId, orderData: $orderData)';
  }
}

/// @nodoc
abstract mixin class $CartVerifyPaymentCopyWith<$Res>
    implements $CartEventCopyWith<$Res> {
  factory $CartVerifyPaymentCopyWith(
          CartVerifyPayment value, $Res Function(CartVerifyPayment) _then) =
      _$CartVerifyPaymentCopyWithImpl;
  @useResult
  $Res call(
      {String razorpayOrderId,
      String razorpayPaymentId,
      String razorpaySignature,
      String orderId,
      Map<String, dynamic> orderData});
}

/// @nodoc
class _$CartVerifyPaymentCopyWithImpl<$Res>
    implements $CartVerifyPaymentCopyWith<$Res> {
  _$CartVerifyPaymentCopyWithImpl(this._self, this._then);

  final CartVerifyPayment _self;
  final $Res Function(CartVerifyPayment) _then;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? razorpayOrderId = null,
    Object? razorpayPaymentId = null,
    Object? razorpaySignature = null,
    Object? orderId = null,
    Object? orderData = null,
  }) {
    return _then(CartVerifyPayment(
      razorpayOrderId: null == razorpayOrderId
          ? _self.razorpayOrderId
          : razorpayOrderId // ignore: cast_nullable_to_non_nullable
              as String,
      razorpayPaymentId: null == razorpayPaymentId
          ? _self.razorpayPaymentId
          : razorpayPaymentId // ignore: cast_nullable_to_non_nullable
              as String,
      razorpaySignature: null == razorpaySignature
          ? _self.razorpaySignature
          : razorpaySignature // ignore: cast_nullable_to_non_nullable
              as String,
      orderId: null == orderId
          ? _self.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
      orderData: null == orderData
          ? _self._orderData
          : orderData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class CartCheckAvailability implements CartEvent {
  const CartCheckAvailability({final List<String>? skus}) : _skus = skus;

  final List<String>? _skus;
  List<String>? get skus {
    final value = _skus;
    if (value == null) return null;
    if (_skus is EqualUnmodifiableListView) return _skus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CartCheckAvailabilityCopyWith<CartCheckAvailability> get copyWith =>
      _$CartCheckAvailabilityCopyWithImpl<CartCheckAvailability>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CartCheckAvailability &&
            const DeepCollectionEquality().equals(other._skus, _skus));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_skus));

  @override
  String toString() {
    return 'CartEvent.checkAvailability(skus: $skus)';
  }
}

/// @nodoc
abstract mixin class $CartCheckAvailabilityCopyWith<$Res>
    implements $CartEventCopyWith<$Res> {
  factory $CartCheckAvailabilityCopyWith(CartCheckAvailability value,
          $Res Function(CartCheckAvailability) _then) =
      _$CartCheckAvailabilityCopyWithImpl;
  @useResult
  $Res call({List<String>? skus});
}

/// @nodoc
class _$CartCheckAvailabilityCopyWithImpl<$Res>
    implements $CartCheckAvailabilityCopyWith<$Res> {
  _$CartCheckAvailabilityCopyWithImpl(this._self, this._then);

  final CartCheckAvailability _self;
  final $Res Function(CartCheckAvailability) _then;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? skus = freezed,
  }) {
    return _then(CartCheckAvailability(
      skus: freezed == skus
          ? _self._skus
          : skus // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

// dart format on
