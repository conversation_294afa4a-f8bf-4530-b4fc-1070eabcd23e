import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../domain/entities/product_entity.dart';

/// A horizontal scrollable variant selector widget similar to Zepto's design
/// Displays product variants in equal-sized rectangular containers
class ProductVariantSelector extends StatelessWidget {
  final List<ProductEntity> variants;
  final ProductEntity? selectedVariant;
  final Function(ProductEntity) onVariantSelected;
  final bool isLoading;

  const ProductVariantSelector({
    super.key,
    required this.variants,
    required this.selectedVariant,
    required this.onVariantSelected,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading || variants.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Options',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.neutral600,
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 48,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 0),
            itemCount: variants.length,
            separatorBuilder: (context, index) => const SizedBox(width: 12),
            itemBuilder: (context, index) {
              final variant = variants[index];
              final isSelected = selectedVariant?.id == variant.id;

              return _buildVariantOption(
                context,
                variant,
                isSelected,
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildVariantOption(
    BuildContext context,
    ProductEntity variant,
    bool isSelected,
  ) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onVariantSelected(variant);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primary.withValues(alpha: 0.1)
              : AppColors.neutral100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.neutral200,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Center(
          child: Text(
            variant.variantName ?? variant.name,
            style: TextStyle(
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              color: isSelected ? AppColors.primary : AppColors.neutral600,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}
