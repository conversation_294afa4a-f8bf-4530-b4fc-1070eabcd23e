// import 'package:freezed_annotation/freezed_annotation.dart';

// part 'home_event.freezed.dart';

// @freezed
// class HomeEvent with _$HomeEvent {
//   const factory HomeEvent.loadBanners() = _LoadBanners;
// }

import 'package:flutter/rendering.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../domain/entities/banner_entity.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../domain/entities/product_entity.dart';

part 'home_event.freezed.dart'; // This file will be generated

@freezed
abstract class HomeEvent with _$HomeEvent {
  const factory HomeEvent.init() = InitHome;
  const factory HomeEvent.updateScroll(bool scroll, double offset) =
      UpdateScroll;
  const factory HomeEvent.updateLoadedList({
    List<CategoryEntity>? categorySections,
    List<CategoryEntity>? categories,
    List<CategoryEntity>? subCategories,
    List<ProductEntity>? previouslyBought,
    List<ProductEntity>? mostPopular,
    List<ProductEntity>? mostBought,
    List<BannerEntity>? banners,
  }) = UpdateHomeList;
  const factory HomeEvent.loadHomeData() = LoadHomeData;
  const factory HomeEvent.loadMoreData() = LoadMoreData;
  const factory HomeEvent.deepLinkFound(
      String route, Map<String, dynamic> args) = LoadDeepLink;
  const factory HomeEvent.scrollDirectionChanged(ScrollDirection direction) =
      ScrollDirectionChanged;
  const factory HomeEvent.switchCategory(CategoryEntity? category, int index) =
      SwitchCategory;
  // You can add more events here if needed, e.g.,
  // const factory HomeEvent.refreshHomeData() = RefreshHomeData;
}
