import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../domain/entities/banner_entity.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../domain/entities/product_entity.dart';

part 'home_state.freezed.dart'; // This file will be generated

@freezed
abstract class HomeState with _$HomeState {
  const factory HomeState.initial({
    @Default(false) bool isScrolled,
    @Default(0) double scrollOffset,
    @Default(true) bool showBottomNavBar,
    @Default(CategoryEntity(
        id: 'My Deals',
        categoryId: 'My Deals',
        name: 'My Deals',
        collectionId: 'My Deals'))
    CategoryEntity selectedCategory,
    @Default(0) int selectedIndex,
  }) = HomeInitial;
  const factory HomeState.loaded({
    required List<CategoryEntity>? categorySections,
    required List<CategoryEntity>? categories,
    required List<CategoryEntity>? subCategories,
    required List<ProductEntity>? mostBought,
    required List<BannerEntity>? banners,
    required bool isScrolled,
    required double scrollOffset,
    @Default(true) bool showBottomNavBar,
    @Default(CategoryEntity(
        id: 'My Deals',
        categoryId: 'My Deals',
        name: 'My Deals',
        collectionId: 'My Deals'))
    CategoryEntity selectedCategory,
    @Default(0) int selectedIndex,
  }) = HomeLoaded;
  const factory HomeState.error({
    required String message,
    required bool isScrolled,
    @Default(0) double scrollOffset,
    @Default(true) bool showBottomNavBar,
    @Default(CategoryEntity(
        id: 'My Deals',
        categoryId: 'My Deals',
        name: 'My Deals',
        collectionId: 'My Deals'))
    CategoryEntity selectedCategory,
    @Default(0) int selectedIndex,
  }) = HomeError;
  const factory HomeState.deepLink({
    required bool isScrolled,
    @Default(0) double scrollOffset,
    @Default(true) bool showBottomNavBar,
    @Default(CategoryEntity(
        id: 'My Deals',
        categoryId: 'My Deals',
        name: 'My Deals',
        collectionId: 'My Deals'))
    CategoryEntity selectedCategory,
    @Default(0) int selectedIndex,
    required String route,
    @Default({}) Map<String, dynamic> args,
  }) = HomeDeepLink;
}
