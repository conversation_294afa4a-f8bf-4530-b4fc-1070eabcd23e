import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/core/utils/notifier.dart';
import 'package:rozana/core/utils/text_field_manager.dart';
import 'package:rozana/domain/entities/order_item_entity.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/app_card.dart';
import 'package:rozana/widgets/custom_button.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_textfield.dart';
import 'package:rozana/widgets/skeleton_loader_factory.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import '../../../../core/widgets/custom_bottom_sheet.dart';
import '../../bloc/order_bloc.dart';
import '../widgets/order_status_chip.dart';
import '../widgets/ravenlink_tracker.dart';
import '../../../../core/utils/helpers.dart';

class OrderDetailsScreen extends StatefulWidget {
  final String orderId;
  final bool fromOrderSuccess;

  const OrderDetailsScreen({
    super.key,
    required this.orderId,
    this.fromOrderSuccess = false,
  });

  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  @override
  void initState() {
    super.initState();
    // Load order details
    context.read<OrderBloc>().add(OrderEvent.loadOrderDetails(widget.orderId));
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: !widget.fromOrderSuccess,
        onPopInvoked: (_) {
          if (widget.fromOrderSuccess) {
            context.go(RouteNames.home);
          }
        },
        child: Scaffold(
          backgroundColor: AppColors.neutral100,
          appBar: AppBar(
            title: CustomText(
              'Order Details',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.neutral600,
            ),
            backgroundColor: AppColors.neutral100,
            elevation: 1,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: AppColors.neutral600),
              onPressed: () {
                if (widget.fromOrderSuccess) {
                  context.go(RouteNames.home);
                } else {
                  context.pop();
                }
              },
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.share, color: AppColors.neutral600),
                onPressed: () => _shareOrderDetails(),
              ),
            ],
          ),
          body: BlocConsumer<OrderBloc, OrderState>(
            listener: (context, state) {
              state.maybeWhen(
                error: (message, orderId) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(message),
                      backgroundColor: AppColors.error,
                    ),
                  );
                },
                orderCancelled: (orderId, message) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(message),
                      backgroundColor: AppColors.success,
                    ),
                  );
                  if (widget.fromOrderSuccess) {
                    context.go(RouteNames.home);
                  } else {
                    context.pop();
                  }
                },
                orElse: () {},
              );
            },
            builder: (context, state) {
              return state.when(
                initial: () => _buildSkeletonLoader(),
                loading: () => _buildSkeletonLoader(),
                orderDetailsLoaded: (order) => _buildOrderDetails(order),
                error: (message, orderId) => _buildErrorState(message),
                orderHistoryLoaded: (_, __, ___, ____, _____) =>
                    _buildSkeletonLoader(),
                empty: (_) => _buildErrorState('Order not found'),
                orderCancelled: (_, __) => _buildSkeletonLoader(),
              );
            },
          ),
        ));
  }

  Widget _buildOrderDetails(OrderEntity order) {
    OrderBloc bloc = context.read<OrderBloc>();
    return RefreshIndicator(
      onRefresh: () async {
        bloc.add(OrderEvent.loadOrderDetails(order.id, showLoader: false));
        await bloc.stream.firstWhere(
          (state) {
            return state.maybeMap(
              orElse: () => false,
              orderDetailsLoaded: (value) {
                return true;
              },
            );
          },
        );
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order header
            _buildOrderHeader(order),
            const SizedBox(height: 16),

            // Ravenlink order tracking
            if (order.invoices.isNotEmpty) ...[
              RavenlinkTracker(invoices: order.invoices, showTitle: false),
              const SizedBox(height: 16),
            ],

            // Order timeline
            _buildOrderTimeline(order),
            const SizedBox(height: 16),

            // Order items
            _buildOrderItems(order),
            const SizedBox(height: 16),

            // Delivery address (only show if address exists)
            if (order.deliveryAddress != null &&
                order.deliveryAddress!.phone != null) ...[
              _buildDeliveryAddress(order),
              const SizedBox(height: 16),
            ],

            // Payment details
            _buildPaymentDetails(order),
            const SizedBox(height: 16),

            // Action buttons
            _buildActionButtons(order),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderHeader(OrderEntity order) {
    return AppCard(
      backgroundColor: AppColors.neutral100,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: CustomText(
                  'Order #${order.id}',
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.neutral600,
                ),
              ),
              OrderStatusChip(status: order.status),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: AppColors.neutral400,
              ),
              const SizedBox(width: 4),
              CustomText(
                'Ordered on ${order.formattedOrderDate}',
                fontSize: 14,
                color: AppColors.neutral400,
              ),
            ],
          ),
          if (order.estimatedDeliveryTime != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.local_shipping,
                  size: 16,
                  color: AppColors.neutral400,
                ),
                const SizedBox(width: 4),
                CustomText(
                  'Estimated delivery: ${formatDeliveryDate(order.estimatedDeliveryTime!)}',
                  fontSize: 14,
                  color: AppColors.neutral400,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOrderTimeline(OrderEntity order) {
    return AppCard(
      backgroundColor: AppColors.neutral100,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            'Order Status',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.neutral600,
          ),
          const SizedBox(height: 16),
          ...order.orderTimeline.asMap().entries.map((entry) {
            final index = entry.key;
            final timeline = entry.value;
            final isLast = index == order.orderTimeline.length - 1;

            return Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                    ),
                    if (!isLast)
                      Container(
                        width: 2,
                        height: 40,
                        color: AppColors.neutral200,
                      ),
                  ],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        timeline.title,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.neutral600,
                      ),
                      const SizedBox(height: 4),
                      CustomText(
                        timeline.description,
                        fontSize: 12,
                        color: AppColors.neutral400,
                      ),
                      const SizedBox(height: 4),
                      CustomText(
                        timeline.formattedDateTime,
                        fontSize: 12,
                        color: AppColors.neutral200,
                      ),
                      if (!isLast) const SizedBox(height: 16),
                    ],
                  ),
                ),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildOrderItems(OrderEntity order) {
    debugPrint('Total Items: ${order.totalItems}');
    return AppCard(
      backgroundColor: AppColors.neutral100,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            'Items (${order.totalItems})',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.neutral600,
          ),
          const SizedBox(height: 16),
          ...order.items.map((item) {
            final bool isReturnable = (item.isReturnable ?? false) &&
                (item.returnType == '10' || item.returnType == '11') &&
                (order.status == '25' || order.status == 'Unknown Status (25)');

            return Padding(
              padding: EdgeInsets.only(bottom: isReturnable ? 8 : 16),
              child: Column(
                children: [
                  Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: CustomImage(
                          imageUrl: item.imageUrl ??
                              'assets/images/image-placeholder.jpg',
                          width: 60,
                          height: 60,
                          fit: BoxFit.cover,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomText(
                              item.name,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: AppColors.neutral600,
                            ),
                            const SizedBox(height: 4),
                            CustomText(
                              '${item.quantity} ${item.unit}',
                              fontSize: 12,
                              color: AppColors.neutral400,
                            ),
                            if (item.facilityName != null) ...[
                              const SizedBox(height: 4),
                              CustomText(
                                'From ${item.facilityName}',
                                fontSize: 12,
                                color: AppColors.neutral200,
                              ),
                            ],
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          CustomText(
                            '₹${item.totalPrice.toStringAsFixed(2)}',
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppColors.neutral600,
                          ),
                          if (item.hasDiscount) ...[
                            const SizedBox(height: 2),
                            Text(
                              '₹${(item.price * item.quantity).toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.neutral200,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                  Visibility(
                    visible: isReturnable,
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: InkWell(
                        onTap: () {
                          _showReturnConfirmationBottomSheet(
                              context, order.id, item);
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.undo_rounded,
                                size: 16,
                                color: AppColors.red400,
                              ),
                              const SizedBox(width: 4.0),
                              CustomText(
                                'Return',
                                fontSize: 12,
                                color: AppColors.red400,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildDeliveryAddress(OrderEntity order) {
    final address = order.deliveryAddress;

    // This method should only be called when address is not null
    if (address == null) {
      return const SizedBox.shrink();
    }

    return AppCard(
      backgroundColor: AppColors.neutral100,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            'Delivery Address',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.neutral600,
          ),
          const SizedBox(height: 12),
          CustomText(
            address.name ?? '',
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.neutral600,
          ),
          const SizedBox(height: 4),
          CustomText(
            '${address.addressLine1 ?? ''}, ${address.addressLine2 ?? ''}',
            fontSize: 14,
            color: AppColors.neutral400,
          ),
          const SizedBox(height: 4),
          CustomText(
            '${address.city ?? ''}, ${address.state ?? ''} - ${address.pincode ?? ''}',
            fontSize: 14,
            color: AppColors.neutral400,
          ),
          if (address.phone != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.phone,
                  size: 16,
                  color: AppColors.neutral400,
                ),
                const SizedBox(width: 4),
                CustomText(
                  address.phone!,
                  fontSize: 14,
                  color: AppColors.neutral400,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentDetails(OrderEntity order) {
    return AppCard(
      backgroundColor: AppColors.neutral100,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            'Payment Details',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.neutral600,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.payment,
                size: 16,
                color: AppColors.neutral400,
              ),
              const SizedBox(width: 8),
              CustomText(
                order.paymentMethod,
                fontSize: 14,
                color: AppColors.neutral400,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.currency_rupee,
                size: 16,
                color: AppColors.neutral400,
              ),
              const SizedBox(width: 8),
              CustomText(
                order.totalAmount.toStringAsFixed(2),
                fontSize: 14,
                color: AppColors.neutral400,
              ),
            ],
          ),
          const SizedBox(height: 8),

          // CustomText();
        ],
      ),
    );
  }

  Widget _buildActionButtons(OrderEntity order) {
    return Column(
      children: [
        if (order.canCancel) ...[
          SizedBox(
            width: double.infinity,
            child: AppButton(
              text: 'Cancel Order',
              onPressed: () =>
                  _showCancelConfirmationBottomSheet(context, order.id),
              backgroundColor: AppColors.error,
              textColor: AppColors.neutral100,
              borderRadius: 8,
              height: 48,
            ),
          ),
          const SizedBox(height: 12),
        ],
        if (order.canReorder) ...[
          SizedBox(
            width: double.infinity,
            child: AppButton(
              text: 'Reorder',
              onPressed: () => _handleReorder(order.id),
              isOutlined: true,
              backgroundColor: AppColors.primary,
              textColor: AppColors.primary,
              borderRadius: 8,
              height: 48,
            ),
          ),
          const SizedBox(height: 12),
        ],
        SizedBox(
          width: double.infinity,
          child: AppButton(
            text: 'Need Help?',
            onPressed: () => _contactSupport(),
            isOutlined: true,
            backgroundColor: AppColors.neutral400,
            textColor: AppColors.neutral400,
            borderRadius: 8,
            height: 48,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            CustomText(
              'Order Not Found',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.neutral600,
            ),
            const SizedBox(height: 8),
            CustomText(
              message,
              fontSize: 14,
              color: AppColors.neutral400,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            AppButton(
              text: 'Go Back',
              onPressed: () => context.pop(),
              backgroundColor: AppColors.primary,
              textColor: AppColors.neutral100,
              borderRadius: 8,
            ),
          ],
        ),
      ),
    );
  }

  void _handleReorder(String orderId) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Reorder functionality will be implemented'),
      ),
    );
  }

  void _shareOrderDetails() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality will be implemented'),
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    return SingleChildScrollView(
      child: SkeletonLoaderFactory.createOrderDetailsSkeleton(),
    );
  }

  void _contactSupport() {
    final existingUserJson = AppPreferences.getUserdata();
    Map<String, dynamic> userData = {};
    if (existingUserJson != null && existingUserJson.isNotEmpty) {
      userData = jsonDecode(existingUserJson);
    }
    context.push(RouteNames.support,
        extra: {'customer_id': userData['enc_cus_code'], 'iv': userData['iv']});
  }
}

void _showReturnConfirmationBottomSheet(
    BuildContext context, String orderId, OrderItemEntity item) {
  TextFieldManager reasonManager = TextFieldManager();

  showConfirmationBottomSheet(
    context: context,
    title: 'Confirm Return',
    message: 'Are you sure you want to proceed with the return for this item?',
    children: [
      Padding(
        padding: const EdgeInsets.fromLTRB(AppDimensions.screenHzPadding, 20,
            AppDimensions.screenHzPadding, 30),
        child: ValueListenableBuilder(
            valueListenable: reasonManager.errorText,
            builder: (context, error, __) {
              return Titledfield(
                title: 'Reason for return (required)',
                fontSize: 12,
                field: CustomTextField(
                  controller: reasonManager.controller,
                  focusNode: reasonManager.focusNode,
                  hintText: 'eg: damaged item, or others',
                ),
                errorText: error,
                errorSize: 12,
              );
            }),
      )
    ],
    backText: 'Cancel',
    onProceed: () async {
      if (reasonManager.text.isEmpty) {
        reasonManager.throwError('Please enter a valid reason for return');
      } else {
        context.pop();
        getIt<AppNotifier>().showLoading();

        OrderBloc bloc = context.read<OrderBloc>();

        bloc.add(OrderEvent.returnOrder(orderId, item, reasonManager.text));
        await bloc.stream.firstWhere(
          (state) {
            getIt<AppNotifier>().hideLoading();
            return true;
          },
        );
      }
    },
    onDispose: () {
      reasonManager.dispose();
    },
  );
}

void _showCancelConfirmationBottomSheet(BuildContext context, String orderId) {
  showConfirmationBottomSheet(
    context: context,
    title: 'Cancel Order?',
    message:
        'This action will cancel your order and cannot be undone.\nAre you sure you want to proceed?',
    onProceed: () {
      context.pop();
      context.read<OrderBloc>().add(OrderEvent.cancelOrder(orderId));
    },
    children: [
      SizedBox(
        height: 24,
      ),
    ],
    onBack: () => context.pop(),
    backText: 'Keep Order',
  );
}
