import 'dart:async';
import 'dart:convert';
import 'dart:ui';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:rozana/core/utils/logger.dart';
import '../config/environment_config.dart';
import '../themes/color_schemes.dart';
import '../utils/color_utils.dart';

class RemoteConfigService {
  static final RemoteConfigService _instance = RemoteConfigService._internal();
  final FirebaseRemoteConfig _remoteConfig = FirebaseRemoteConfig.instance;

  final Completer<void> _initializationCompleter = Completer<void>();

  Future<void> get initialized => _initializationCompleter.future;

  factory RemoteConfigService() => _instance;

  RemoteConfigService._internal();

  Future<void> initialize() async {
    try {
      await _remoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 5),
        minimumFetchInterval: Duration.zero,
      ));

      await _remoteConfig.fetchAndActivate().timeout(
        const Duration(seconds: 3),
        onTimeout: () {
          return false;
        },
      );

      if (!_initializationCompleter.isCompleted) {
        _initializationCompleter.complete();
      }
    } catch (e) {
      if (!_initializationCompleter.isCompleted) {
        _initializationCompleter.complete();
      }
    }
  }

  /// Force refresh remote config values regardless of cache expiration
  /// Returns true if values were fetched from the server, false otherwise
  Future<bool> forceRefresh() async {
    try {
      final fetchedRemotely = await _remoteConfig.fetchAndActivate();
      return fetchedRemotely;
    } catch (e) {
      return false;
    }
  }

  /// Get the environment config for the current environment
  Map<String, dynamic> get _currentEnvConfig {
    try {
      final String envKey = EnvironmentConfig.environment.name;
      final configValue = _remoteConfig.getValue(envKey);
      if (configValue.source == ValueSource.valueStatic) {
        return _getDefaultConfig();
      }
      final configString = configValue.asString();
      if (configString.isEmpty) {
        return _getDefaultConfig();
      }
      try {
        final Map<String, dynamic> parsedJson = json.decode(configString);
        return parsedJson;
      } catch (e) {
        return _getDefaultConfig();
      }
    } catch (e) {
      return _getDefaultConfig();
    }
  }

  Map<String, dynamic> get getThemeConfig {
    try {
      final configString = _remoteConfig.getString('theme_settings');

      if (configString.isEmpty) {
        return {};
      }

      try {
        final Map<String, dynamic> decodedJson = json.decode(configString);
        return decodedJson;
      } catch (e1) {
        LogMessage.l("Initial JSON decode failed for List: $e1");

        String processedString = configString;

        if (processedString.startsWith('"') && processedString.endsWith('"')) {
          processedString =
              processedString.substring(1, processedString.length - 1);
        }

        processedString = processedString.replaceAll('\\"', '"');
        LogMessage.l("Processed String for decode: $processedString");

        try {
          final Map<String, dynamic> decodedJson = json.decode(processedString);
          return decodedJson;
        } catch (e2) {
          LogMessage.l("Second (processed) JSON decode failed for List: $e2");
          return {};
        }
      }
    } catch (e) {
      LogMessage.l(
          "Error retrieving or processing theme_settings from Remote Config: $e");
      return {};
    }
  }

  Map<String, Color?> get getDynamicColors {
    try {
      final configString = _remoteConfig.getString('app_colors_json');

      Map<String, Color?> appColors = {};

      if (configString.isEmpty) {
        return appColors;
      }

      try {
        final Map<String, dynamic> decodedJson = jsonDecode(configString);
        appColors = decodedJson.map((key, value) =>
            MapEntry(key, ColorUtils.hexToColor(value.toString())));
        return appColors;
      } catch (e1) {
        LogMessage.l("Initial JSON decode failed for List: $e1");

        String processedString = configString;

        if (processedString.startsWith('"') && processedString.endsWith('"')) {
          processedString =
              processedString.substring(1, processedString.length - 1);
        }

        processedString = processedString.replaceAll('\\"', '"');
        LogMessage.l("Processed String for decode: $processedString");

        try {
          final Map<String, dynamic> decodedJson = jsonDecode(processedString);
          appColors = decodedJson.map((key, value) =>
              MapEntry(key, ColorUtils.hexToColor(value.toString())));
          return appColors;
        } catch (e2) {
          // Handle JSON parsing errors, fall back to defaults
          appColors = AppColors.defaultColors;
          return appColors;
        }
      }
    } catch (e) {
      // Handle JSON parsing errors, fall back to defaults
      return AppColors.defaultColors;
    }
  }

  /// Get the Typesense config from the environment config
  Map<String, dynamic> get _typesenseConfig {
    final envConfig = _currentEnvConfig;
    if (envConfig.containsKey('typesense')) {
      return envConfig['typesense'];
    } else {
      return _getDefaultTypesenseConfig();
    }
  }

  String get razorpayKey {
    final envConfig = _currentEnvConfig;
    return envConfig['razorpay_key'] ?? 'rzp_test_Jagi7su7aEjQ9P';
  }

  String get omsBaseUrl {
    final envConfig = _currentEnvConfig;
    return envConfig['omsBaseUrl'] ?? '';
  }

  String get imsBaseUrl {
    final envConfig = _currentEnvConfig;
    return envConfig['imsBaseUrl'] ?? '';
  }

  String get walletBaseUrl {
    final envConfig = _currentEnvConfig;
    return envConfig['walletBaseUrl'] ?? '';
  }

  String get walletAPIKey {
    final envConfig = _currentEnvConfig;
    return envConfig['walletAPIKey'] ?? '';
  }

  /// Get default config from the defaults JSON
  Map<String, dynamic> _getDefaultConfig() {
    return {
      "typesense": _getDefaultTypesenseConfig(),
      "razorpay_key": 'rzp_test_Jagi7su7aEjQ9P',
      "omsBaseUrl": '',
      "imsBaseUrl": '',
      "walletBaseUrl": '',
      "walletAPIKey": ''
    };
  }

  /// Get default Typesense config as fallback
  Map<String, dynamic> _getDefaultTypesenseConfig() {
    return {
      "api_key": EnvironmentConfig.typesenseApiKey,
      "host": EnvironmentConfig.typesenseHost,
      "port": EnvironmentConfig.typesensePort,
      "protocol": EnvironmentConfig.typesenseProtocol,
      "collections": {
        "products": "facility_products",
        "categories": "store_categories",
        "stores": "stores",
        "top_products": "top_products"
      }
    };
  }

  /// Get the Typesense API key based on current environment
  String get typesenseApiKey => _typesenseConfig['api_key'];

  /// Get the Typesense host based on current environment
  String get typesenseHost => _typesenseConfig['host'];

  /// Get the Typesense port based on current environment
  String get typesensePort => _typesenseConfig['port'];

  /// Get the Typesense protocol based on current environment
  String get typesenseProtocol => _typesenseConfig['protocol'];

  Map<String, dynamic> get typesenseExtras => _typesenseConfig['extras'] ?? {};

  /// Get collection names
  Map<String, dynamic> get _collections =>
      _typesenseConfig['collections'] ??
      {
        "products": "facility_products",
        "categories": "store_categories",
        "stores": "stores",
        "top_products": "top_products",
        "sliders": "sliders"
      };

  String get productsCollection =>
      _collections['products'] ?? 'facility_products';

  String get categoriesCollection =>
      _collections['categories'] ?? 'store_categories';

  String get storesCollection => _collections['stores'] ?? 'stores';

  String get topProductsCollection =>
      _collections['top_products'] ?? 'top_products';

  String get slidersCollection => _collections['sliders'] ?? 'sliders';
  String get sectionConfig =>
      _collections['section_config'] ?? 'section_config';
}
