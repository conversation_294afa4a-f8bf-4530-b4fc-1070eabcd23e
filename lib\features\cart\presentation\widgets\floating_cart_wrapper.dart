import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/features/cart/presentation/widgets/floating_cart_button.dart';

import '../../../location/bloc/location bloc/location_bloc.dart';
import '../../../order/presentation/widgets/order_tracking_wrapper.dart';

class FloatingCartWrapper extends StatelessWidget {
  final Widget child;

  final bool excludeFloatingCart;
  final bool excludeOrderTile;

  final double bottomPadding;

  const FloatingCartWrapper({
    super.key,
    required this.child,
    this.excludeFloatingCart = false,
    this.bottomPadding = 0,
    this.excludeOrderTile = false,
  });

  @override
  Widget build(BuildContext context) {
    if (excludeFloatingCart) {
      return child;
    }

    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        child,
        BlocBuilder<LocationBloc, LocationState>(
          builder: (ctx, state) {
            return state.maybeWhen(
              notServiceable: (address) => SizedBox.shrink(),
              orElse: () => BlocBuilder<CartBloc, CartState>(
                buildWhen: (previous, current) =>
                    previous.cart.totalItems != current.cart.totalItems,
                builder: (ctx, state) {
                  final appBloc = context.read<AppBloc>();
                  final bool isAuthenticated = appBloc.isAuthenticated;
                  if (state.cart.totalItems == 0 && isAuthenticated) {
                    return OrderTrackingWrapper(
                      excludeOrderTile: excludeFloatingCart || excludeOrderTile,
                      child: child,
                    );
                  }

                  return SafeArea(child: const FloatingCartButton());
                },
              ),
            );
          },
        ),
      ],
    );
  }
}
