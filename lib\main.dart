import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/app/app_config.dart';

import 'app/app.dart';
import 'core/dependency_injection/di_container.dart';
import 'core/dependency_injection/di_setup.dart';
import 'app/bloc/app_bloc.dart';

void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();
  // Setup dependency injection
  setupDI();
  await AppConfig.init(); // Initialize app configuration

  runApp(
    // Provide the AppBloc at the root of the widget tree
    // This allows the splash screen to determine initial routing
    BlocProvider(
      create: (context) => getIt<AppBloc>()..add(const AppStarted()),
      child: const MyApp(),
    ),
  );
}
