export 'search_event.dart';
export 'search_state.dart';

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/utils/debouncer.dart';
import '../../../../core/services/appflyer_services/appflyer_events.dart';
import '../../../../core/utils/text_field_manager.dart';
import '../../services/typesense_service.dart';
import 'search_event.dart';
import 'search_state.dart';

class SearchBloc extends Bloc<SearchEvent, SearchState> {
  final TypesenseService _searchService;

  final int _pageSize = 30;
  static bool enableSearchPagination = true;

  final Debouncer _suggestionDebouncer = Debouncer(milliseconds: 200);
  final Debouncer _searchDebouncer = Debouncer(milliseconds: 1000);

  static TextFieldManager? searchFieldManager;
  static ScrollController? scrollController;

  SearchBloc(this._searchService) : super(SearchState.initial()) {
    searchFieldManager = TextFieldManager();
    scrollController = ScrollController();
    on<SearchEvent>(
      (event, emit) => event.map(
        init: (e) async => await _onInit(e.initialQuery, e.dynamicQuery, emit),
        search: (e) async =>
            await _onSearch(e.query, e.dynamicQuery, e.submit, e.sku, emit),
        loadMore: (e) async => await _onLoadMore(emit),
        clearSearch: (e) async => await _onClearSearch(emit),
        clearRecent: (e) async => await _onClearRecent(emit),
        removeRecent: (e) async => await _onRemoveRecent(e.index, emit),
        selectRecent: (e) async => await _onSelectRecent(e.query, emit),
        selectSuggestion: (e) async =>
            await _onSelectSuggestion(e.suggestion, e.sku, emit),
        inputChange: (e) async => await _onInputChange(e.query, emit),
        updateSuggestions: (e) async =>
            await _onUpdateSuggestions(e.query, emit),
        clearState: (e) async => await _onClearState(emit),
      ),
    );
  }

  Future<void> _onInit(String initialQuery, Map<String, dynamic>? dynamicQuery,
      Emitter<SearchState> emit) async {
    try {
      if (dynamicQuery?.isNotEmpty ?? false) {
        searchFieldManager?.focusNode.unfocus();
        add(SearchEvent.search('', dynamicQuery: dynamicQuery));
      } else if (initialQuery.isNotEmpty) {
        add(SearchEvent.search(initialQuery));
      } else {
        searchFieldManager?.focusNode.requestFocus();

        final json = AppPreferences.getRecentSearches();
        if (json != null) {
          final decoded = List<String>.from(jsonDecode(json));
          emit(state.copyWith(recentSearches: decoded));
        }
      }
    } catch (_) {}
  }

  Future<void> _onSearch(String query, Map<String, dynamic>? dynamicQuery,
      bool submitSearch, String? sku, Emitter<SearchState> emit) async {
    String queryString = query;

    if (dynamicQuery?.isNotEmpty ?? false) {
      final Map<String, dynamic> queryMap =
          jsonDecode(dynamicQuery!['query']) ?? {};

      queryString = (queryMap.isNotEmpty)
          ? (queryMap['filter_by'].toString().split(':=').last)
          : query;
    }

    emit(state.copyWith(
        query: queryString, page: 1, hasMore: enableSearchPagination));

    try {
      Map<String, List<Map<String, dynamic>>> results = {};

      List<Map<String, dynamic>> allProducts = [];

      if (dynamicQuery?.isNotEmpty ?? false) {
        Map<String, List<dynamic>> data =
            await _searchService.getDataByDynamicQuery(
                query: dynamicQuery!['query'],
                page: 1,
                pageSize: _pageSize,
                queryCollection: dynamicQuery['collection']);
        allProducts = data['products'] as List<Map<String, dynamic>>;
      } else {
        results = await _searchService.globalSearch(
          query: query,
          page: 1,
          pageSize: _pageSize,
          sku: sku,
        );
        allProducts = List.from(results['products'] ?? []);
      }

      if (allProducts.isNotEmpty) {
        final firstProduct = allProducts.first;
        final categoryId = firstProduct['categoryId']?.toString();
        final brandId = firstProduct['brand_id']?.toString();
        final firstProductId = firstProduct['id']?.toString();
        if (categoryId != null && firstProductId != null) {
          try {
            final relatedProducts = await _searchService.searchProducts(
              categoryId: categoryId,
              excludeProductId: firstProductId,
              pageSize: 10,
              page: 1,
            );
            for (final relatedProduct in relatedProducts) {
              final relatedProductId = relatedProduct['id']?.toString();
              if (relatedProductId != null &&
                  !allProducts.any((product) =>
                      product['id']?.toString() == relatedProductId)) {
                allProducts.add(relatedProduct);
              }
            }
          } catch (e) {
            debugPrint('Error fetching related products: $e');
          }
        }

        if (brandId != null) {
          try {
            final brandProducts = await _searchService.searchProducts(
              brandId: brandId,
              excludeProductId: firstProductId,
              pageSize: 10,
              page: 1,
            );

            for (final brandProduct in brandProducts) {
              final brandProductId = brandProduct['id']?.toString();
              if (brandProductId != null &&
                  !allProducts.any((product) =>
                      product['id']?.toString() == brandProductId)) {
                allProducts.add(brandProduct);
              }
            }
          } catch (e) {
            debugPrint('Error fetching brand products: $e');
          }
        }
      }

      emit(state.copyWith(
        isLoading: false,
        products: allProducts,
        categories: results['categories'] ?? [],
        subcategories: results['subcategories'] ?? [],
        hasMore: enableSearchPagination &&
            ((results['products']?.length ?? 0) >= _pageSize),
        suggestions: submitSearch ? [] : state.suggestions,
      ));

      if (dynamicQuery?.isEmpty ?? true) {
        await _saveRecentSearch(query, emit);
        await AppsFlyerEvents.search(query);
      }
    } catch (e) {
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
  }

  Future<void> _onLoadMore(Emitter<SearchState> emit) async {
    if (!enableSearchPagination) return;

    if (state.isLoading || !state.hasMore) return;

    final nextPage = state.page + 1;

    emit(state.copyWith(isLoading: true, page: nextPage));

    try {
      final results = await _searchService.globalSearch(
        query: state.query,
        page: nextPage,
        pageSize: _pageSize,
      );

      emit(state.copyWith(
        isLoading: false,
        products: [...state.products, ...results['products'] ?? []],
        hasMore: enableSearchPagination &&
            ((results['products']?.length ?? 0) >= _pageSize),
      ));
    } catch (e) {
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
  }

  Future<void> _onClearSearch(Emitter<SearchState> emit) async {
    try {
      emit(state.copyWith(products: [], suggestions: [], query: ''));
    } catch (_) {}
  }

  Future<void> _onClearRecent(Emitter<SearchState> emit) async {
    try {
      AppPreferences.clearOne(AppPreferences.recentSearches);
      emit(state.copyWith(recentSearches: []));
    } catch (_) {}
  }

  Future<void> _onRemoveRecent(int index, Emitter<SearchState> emit) async {
    final updatedRecents = List<String>.from(state.recentSearches);
    if (index >= 0 && index < updatedRecents.length) {
      updatedRecents.removeAt(index);
      await AppPreferences.setRecentSearches(jsonEncode(updatedRecents));
      emit(state.copyWith(recentSearches: updatedRecents));
    }
  }

  Future<void> _onSelectRecent(String query, Emitter<SearchState> emit) async {
    searchFieldManager?.text = query;
    searchFieldManager?.focusNode.unfocus();
    add(SearchEvent.search(query, submit: true));
  }

  Future<void> _onSelectSuggestion(
      String suggestion, String? sku, Emitter<SearchState> emit) async {
    searchFieldManager?.text = suggestion;
    searchFieldManager?.focusNode.unfocus();
    add(SearchEvent.search(suggestion, submit: true, sku: sku));
  }

  Future<void> _onInputChange(String query, Emitter<SearchState> emit) async {
    try {
      if (query.length >= 2) {
        // Immediately emit loading state when user starts typing
        emit(state.copyWith(isLoading: true, query: query));

        _suggestionDebouncer.run(() async {
          add(SearchEvent.updateSuggestions(query));
        });

        _searchDebouncer.run(() {
          if (query == state.query) {
            add(SearchEvent.search(query));
          }
        });
      } else {
        _suggestionDebouncer.run(() {
          add(SearchEvent.clearSearch());
        });
        // Clear loading state if query is too short
        emit(state.copyWith(isLoading: false));
      }
    } catch (_) {}
  }

  Future<void> _onUpdateSuggestions(
      String query, Emitter<SearchState> emit) async {
    try {
      final suggestions = await _searchService.getSearchSuggestions(query);
      emit(state.copyWith(
        suggestions: suggestions,
        query: query,
      ));
    } catch (_) {
      // Ensure loading state is turned off on error
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> _saveRecentSearch(
      String query, Emitter<SearchState> emit) async {
    final searches = [...state.recentSearches];
    searches.remove(query);
    searches.insert(0, query);
    if (searches.length > 10) {
      searches.removeRange(10, searches.length);
    }

    await AppPreferences.setRecentSearches(jsonEncode(searches));
    emit(state.copyWith(recentSearches: searches));
  }

  Future<void> _onClearState(Emitter<SearchState> emit) async {
    try {
      emit(SearchState.initial());
    } catch (_) {}
  }

  @override
  Future<void> close() {
    _suggestionDebouncer.dispose();
    _searchDebouncer.dispose();
    searchFieldManager?.dispose();
    scrollController?.dispose();
    return super.close();
  }
}
