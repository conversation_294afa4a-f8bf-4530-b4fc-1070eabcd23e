import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:flutter/services.dart';

import '../core/themes/color_schemes.dart';
import '../features/search/bloc/bloc/search_bloc.dart';
import 'custom_text.dart';
import 'shimmer_widgets.dart';

class CustomInputBorder extends OutlineInputBorder {
  const CustomInputBorder({
    super.borderSide,
    this.radius,
  });

  final double? radius;

  @override
  BorderSide get borderSide => BorderSide(color: AppColors.neutral200);

  @override
  BorderRadius get borderRadius =>
      BorderRadius.all(Radius.circular(radius ?? 6));
}

class TransparentInputBorder extends OutlineInputBorder {
  const TransparentInputBorder({
    super.borderSide = const BorderSide(color: Colors.transparent),
    this.radius,
  });

  final double? radius;

  @override
  BorderRadius get borderRadius =>
      BorderRadius.all(Radius.circular(radius ?? 100));
}

class CustomTextField extends TextField {
  const CustomTextField({
    super.key,
    super.controller,
    super.focusNode,
    super.undoController,
    super.decoration,
    super.keyboardType = TextInputType.text,
    super.textInputAction = TextInputAction.next,
    super.textCapitalization = TextCapitalization.sentences,
    super.style,
    super.strutStyle,
    super.textAlign = TextAlign.start,
    super.textAlignVertical,
    super.textDirection,
    super.readOnly = false,
    super.toolbarOptions,
    super.showCursor,
    super.autofocus = false,
    super.obscuringCharacter = '•',
    super.obscureText = false,
    super.autocorrect = true,
    super.enableSuggestions = true,
    super.maxLines = 1,
    super.minLines,
    super.expands = false,
    super.maxLength,
    super.maxLengthEnforcement,
    super.onChanged,
    super.onEditingComplete,
    super.onSubmitted,
    super.onAppPrivateCommand,
    super.inputFormatters,
    super.enabled,
    super.cursorWidth = 2.0,
    super.cursorHeight,
    super.cursorRadius,
    super.cursorOpacityAnimates,
    super.cursorColor,
    super.keyboardAppearance,
    super.scrollPadding = const EdgeInsets.only(bottom: 200),
    super.dragStartBehavior = DragStartBehavior.start,
    bool? enableInteractiveSelection,
    super.selectionControls,
    super.onTap,
    super.onTapOutside,
    super.mouseCursor,
    super.buildCounter,
    super.scrollController,
    super.scrollPhysics,
    super.autofillHints = const <String>[],
    super.canRequestFocus = true,
    this.isMandatory = false,
    this.hasLabel = false,
    this.hintText,
    this.borderRadius = 10,
    this.padding,
  });

  final bool isMandatory;
  final bool hasLabel;
  final String? hintText;
  final double? borderRadius;
  final EdgeInsets? padding;

  @override
  TapRegionCallback? get onTapOutside => (event) {
        focusNode?.unfocus();
      };

  @override
  List<TextInputFormatter>? get inputFormatters =>
      super.inputFormatters ??
      [FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9\s]'))];

  @override
  TextStyle? get style => (super.enabled ?? true)
      ? super.style ??
          TextStyle(
            fontSize: 14,
            color: AppColors.neutral600,
            fontWeight: FontWeight.w500,
            height: 1.4,
            overflow: TextOverflow.ellipsis,
          )
      : null;

  @override
  InputDecoration? get decoration => super.decoration?.copyWith(
        hintText: hintText,
        contentPadding: padding ?? const EdgeInsets.fromLTRB(16, 20, 16, 10),
        border:
            super.decoration?.border ?? CustomInputBorder(radius: borderRadius),
        enabledBorder: super.decoration?.enabledBorder ??
            CustomInputBorder(radius: borderRadius),
        disabledBorder: super.decoration?.disabledBorder ??
            CustomInputBorder(radius: borderRadius),
        focusedBorder: super.decoration?.focusedBorder ??
            CustomInputBorder(radius: borderRadius),
        labelText: hasLabel && isMandatory ? '*' : null,
        filled: true,
        fillColor: super.decoration?.fillColor ?? AppColors.neutral100,
        floatingLabelStyle: TextStyle(
            color: AppColors.primary,
            fontSize: 16,
            fontWeight: FontWeight.w500),
        labelStyle: TextStyle(
            color: AppColors.primary,
            fontSize: 14,
            fontWeight: FontWeight.w500),
        hintStyle: super.decoration?.hintStyle ??
            TextStyle(
              fontSize: 14,
              color: AppColors.neutral500,
              // fontWeight: FontWeight.w500,
              overflow: TextOverflow.ellipsis,
            ),
        helperStyle: const TextStyle(
            // color: Get.theme.primaryBlue,
            fontSize: 12,
            fontWeight: FontWeight.w400,
            overflow: TextOverflow.visible),
        helperMaxLines: 5,
        counter: const SizedBox(),
      );
}

class SearchTextField extends CustomTextField {
  const SearchTextField(
      {super.key,
      super.controller,
      super.focusNode,
      super.keyboardType = TextInputType.text,
      super.textInputAction = TextInputAction.search,
      super.textCapitalization = TextCapitalization.sentences,
      super.decoration,
      super.isMandatory = false,
      super.hasLabel = false,
      super.hintText = 'Search',
      super.readOnly = false,
      super.enabled = true,
      super.onChanged,
      super.onSubmitted,
      super.autofocus = true,
      super.borderRadius = 12,
      this.onClear,
      this.onBackPressed,
      this.showBorder = false,
      super.onTapOutside,
      super.inputFormatters});
  final VoidCallback? onClear;
  final VoidCallback? onBackPressed;
  final bool showBorder;

  @override
  TextStyle? get style => (super.enabled ?? true)
      ? TextStyle(
          fontSize: 16,
          color: AppColors.neutral700,
          fontWeight: FontWeight.w500,
          height: 1.4,
          overflow: TextOverflow.ellipsis,
        )
      : null;

  @override
  InputDecoration? get decoration => super.decoration?.copyWith(
        hintText: hintText ?? 'Search for fashion, electronics, groceries...',
        hintStyle: TextStyle(
          fontSize: 16.sp,
          color: AppColors.primary300,
        ),
        fillColor: AppColors.neutral100,
        contentPadding: super.decoration?.contentPadding ??
            EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        prefixIcon: onBackPressed != null
            ? IconButton(
                icon: Image.asset(
                  'assets/new/icons/chevron_left.png',
                  height: 24,
                  width: 24,
                ),
                onPressed: onBackPressed,
                constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
              )
            : Icon(
                Icons.search,
                color: AppColors.neutral400,
                size: 20,
              ),
        prefixIconConstraints:
            const BoxConstraints(minWidth: 36, minHeight: 36),
        suffixIcon: controller != null && controller!.text.isNotEmpty
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Image.asset(
                      'assets/new/icons/cancel.png',
                      height: 24,
                      width: 24,
                    ),
                    constraints:
                        const BoxConstraints(minWidth: 36, minHeight: 36),
                    onPressed: () {
                      controller!.clear();
                      if (onClear != null) {
                        onClear!();
                      } else if (onChanged != null) {
                        onChanged!('');
                      }
                    },
                  ),
                  BlocBuilder<SearchBloc, SearchState>(
                    builder: (context, state) {
                      return state.isLoading
                          ? Padding(
                              padding:
                                  const EdgeInsets.only(right: 16, left: 4),
                              child: SizedBox.square(
                                dimension: 18,
                                child: CircularProgressIndicator(
                                  color: AppColors.primary,
                                  strokeWidth: 3,
                                ),
                              ),
                            )
                          : SizedBox();
                    },
                  )
                ],
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 12),
          borderSide: BorderSide(color: AppColors.primary200, width: 0.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 12),
          borderSide: BorderSide(color: AppColors.primary200, width: 0.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 12),
          borderSide: BorderSide(color: AppColors.primary200, width: 0.5),
        ),
      );
}

class Titledfield extends StatelessWidget {
  const Titledfield({
    super.key,
    required this.title,
    required this.field,
    this.hasBottomSpacing = false,
    this.isLoading = false,
    this.titleWidget,
    this.bottomSpacing,
    this.fontSize,
    this.opacity = 1,
    this.errorText,
    this.errorSize,
  });

  final String title;
  final Widget? titleWidget;
  final Widget field;
  final bool hasBottomSpacing;
  final double? bottomSpacing;
  final bool isLoading;
  final double? fontSize;
  final double opacity;
  final String? errorText;
  final double? errorSize;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        titleWidget ??
            MainHeaderText(
              title,
              fontSize: fontSize ?? 14,
              color: AppColors.neutral600,
            ),
        const SizedBox(height: 6),
        isLoading
            ? const ShimmerTextField()
            : Opacity(opacity: opacity, child: field),
        Visibility(
          visible: errorText?.isNotEmpty ?? false,
          child: Padding(
            padding: const EdgeInsets.only(top: 8, left: 10),
            child: CustomText(
              errorText ?? '',
              color: AppColors.error,
              overflow: TextOverflow.visible,
              fontSize: errorSize,
            ),
          ),
        ),
        SizedBox(height: hasBottomSpacing ? (bottomSpacing ?? 24) : 0),
      ],
    );
  }
}

class FieldErrorText extends StatelessWidget {
  const FieldErrorText({super.key, required this.errorText});
  final String errorText;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8, left: 10),
      child: CustomText(
        errorText,
        color: AppColors.primary,
      ),
    );
  }
}
