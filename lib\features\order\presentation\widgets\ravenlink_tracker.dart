import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/app_card.dart';
import 'package:carousel_slider/carousel_slider.dart';

/// A widget that displays a Ravenlink tracking iframe for order tracking
class RavenlinkTracker extends StatefulWidget {
  final List<Map<String, dynamic>>? invoices;
  final double height;
  final bool showTitle;

  const RavenlinkTracker({
    super.key,
    this.invoices,
    this.height = 400,
    this.showTitle = true,
  });

  @override
  State<RavenlinkTracker> createState() => _RavenlinkTrackerState();
}

class _RavenlinkTrackerState extends State<RavenlinkTracker> {
  late List<WebViewController> _controllers = [];
  List<bool> _isLoading = [];
  int _currentIndex = 0;
  List<Map<String, dynamic>> _invoiceData = [];

  @override
  void initState() {
    super.initState();
    _setupData();
    _initWebViews();
  }

  void _setupData() {
    if (widget.invoices != null && widget.invoices!.isNotEmpty) {
      _invoiceData = widget.invoices!;
    }
    _isLoading = List.generate(_invoiceData.length, (_) => true);
  }

  void _initWebViews() {
    _controllers = _invoiceData.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;
      final url = data['raven_link'] as String? ?? '';
      return WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(const Color(0x00000000))
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              setState(() {
                _isLoading[index] = true;
              });
            },
            onPageFinished: (String url) {
              setState(() {
                _isLoading[index] = false;
              });
            },
            onWebResourceError: (WebResourceError error) {
              debugPrint('Ravenlink WebView error: ${error.description}');
            },
          ),
        )
        ..loadRequest(Uri.parse(url));
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    if (_controllers.isEmpty) {
      return const SizedBox.shrink();
    }

    return AppCard(
      backgroundColor: AppColors.neutral100,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showTitle) ...[
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 18,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                CustomText(
                  'Live Order Tracking',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.neutral600,
                ),
              ],
            ),
            const SizedBox(height: 12),
          ],

          // Carousel for multiple trackers
          SizedBox(
            height: widget.height,
            child: Stack(
              children: [
                CarouselSlider.builder(
                  itemCount: _controllers.length,
                  options: CarouselOptions(
                    height: widget.height,
                    viewportFraction: 1.0,
                    enableInfiniteScroll: false,
                    onPageChanged: (index, reason) {
                      setState(() {
                        _currentIndex = index;
                      });
                    },
                  ),
                  itemBuilder: (context, index, realIndex) {
                    return Stack(
                      children: [
                        WebViewWidget(controller: _controllers[index]),
                        if (_isLoading[index])
                          Center(
                            child: CircularProgressIndicator(
                              color: AppColors.primary,
                            ),
                          ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),

          // Dots indicator for multiple trackers
          if (_invoiceData.length > 1) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _invoiceData.asMap().entries.map((entry) {
                final index = entry.key;
                return Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentIndex == index
                        ? AppColors.primary
                        : AppColors.neutral300,
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }
}
