import 'package:flutter/foundation.dart';
import '../../features/products/bloc/listing_bloc/product_listing_bloc.dart';
import '../../features/products/bloc/listing_bloc/product_listing_event.dart';
import '../dependency_injection/di_container.dart';
import '../../features/cart/bloc/cart_bloc.dart';
import '../../features/categories/bloc/categories_bloc.dart';
import '../../features/location/bloc/location%20bloc/location_bloc.dart';
import '../../features/location/services/adress_services.dart';

/// Service to handle BLoC cleanup and reset operations
/// This helps manage memory and state when users log out or app restarts
class BlocCleanupService {
  /// Reset all user-specific BLoCs to their initial state
  /// Call this when user logs out to prevent data leakage between users
  static void resetUserSpecificBlocs() {
    try {
      // Reset CartBloc - clear all cart items and user-specific data
      final cartBloc = getIt<CartBloc>();
      cartBloc.resetCart();

      // Reset LocationBloc - clear user-specific location data
      final locationBloc = getIt<LocationBloc>();
      locationBloc.resetLocation();

      // Reset AddressService - clear all address data and callbacks
      final addressService = getIt<AddressService>();
      addressService.dispose();

      // Note: ThemeBloc, LanguageBloc, and WebViewBloc are global state
      // and should not be reset on user logout
    } catch (e) {
      // Log error but don't throw to prevent app crashes
      debugPrint('Error resetting BLoCs: $e');
    }
  }

  /// Dispose all BLoCs - use this only when app is being terminated
  /// This is mainly for testing or complete app shutdown
  static Future<void> disposeAllBlocs() async {
    try {
      // Note: In production, GetIt singletons are typically not disposed
      // unless the entire app is shutting down

      // For testing purposes, you might want to reset GetIt
      // getIt.reset(); // Uncomment only for testing
    } catch (e) {
      debugPrint('Error disposing BLoCs: $e');
    }
  }

  /// Check if all critical BLoCs are properly initialized
  static bool areBloCsInitialized() {
    try {
      getIt<CartBloc>();
      getIt<LocationBloc>();
      return true;
    } catch (e) {
      return false;
    }
  }

  static void resetOnAddressChange() {
    try {
      try {
        final categoriesBloc = getIt<CategoriesBloc>();
        categoriesBloc.add(const CategoriesEvent.fetchCategories());
      } catch (e) {
        debugPrint('CategoriesBloc not initialized or error: $e');
      }

      try {
        final productListingBloc = getIt<ProductListingBloc>();
        productListingBloc.add(const ProductListingEvent.reset());
      } catch (e) {
        debugPrint('ProductListingBloc not initialized or error: $e');
      }
    } catch (e) {
      debugPrint('Error resetting BLoCs on address change: $e');
    }
  }
}
