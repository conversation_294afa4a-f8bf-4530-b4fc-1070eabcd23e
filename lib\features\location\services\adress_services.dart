import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:rozana/features/location/services/google_places_service.dart';

import 'locaion_services.dart';
import '../../../core/services/firestore_address_service.dart';
import '../../../core/dependency_injection/di_container.dart';
import '../../../core/utils/logger.dart';
import '../../../data/models/adress_model.dart';
import '../../../core/services/app_preferences_service.dart';
import 'package:rozana/core/services/bloc_cleanup_service.dart';

class AddressService {
  static final AddressService _instance = AddressService._internal();
  factory AddressService() => _instance;
  AddressService._internal();

  final LocationService _locationService = LocationService();
  FirestoreAddressService? _firestoreService;
  final GooglePlacesService _googlePlacesService = GooglePlacesService();
  static const double _deliveryRadiusMeters = 100.0;

  // Simple state management
  List<AddressModel> _addresses = [];
  AddressModel? _currentSelectedAddress;
  Position? _lastKnownPosition;
  bool _addressesLoaded = false;

  /// Get Firestore service instance
  FirestoreAddressService get _firestore {
    _firestoreService ??= getIt<FirestoreAddressService>();
    return _firestoreService!;
  }

  /// Check if user is authenticated
  bool get isAuthenticated => _firestore.isUserAuthenticated;

  /// Get current selected address (cached, no location detection)
  AddressModel? get currentSelectedAddress => _currentSelectedAddress;

  /// Get current selected address without location detection (for UI display)
  AddressModel? getCurrentSelectedAddressSync() {
    return _currentSelectedAddress;
  }

  /// Manually set selected address (when user selects from address list)
  void setSelectedAddress(AddressModel address) {
    _currentSelectedAddress = address;
    LogMessage.p('Manually selected address: ${address.addressType}');
    // Store selected address ID locally
    if (address.id != null) {
      AppPreferences.setSelectedAddressId(address.id!);
    }
    // Notify that address has been manually selected
    _notifyAddressChanged();
    BlocCleanupService.resetOnAddressChange();
  }

  /// Callback to notify when address changes (for LocationBloc)
  Function(AddressModel)? _onAddressChanged;

  /// Set callback for address changes
  void setOnAddressChanged(Function(AddressModel) callback) {
    _onAddressChanged = callback;
  }

  /// Notify listeners that address has changed
  void _notifyAddressChanged() {
    if (_currentSelectedAddress != null && _onAddressChanged != null) {
      _onAddressChanged!(_currentSelectedAddress!);
    }
  }

  /// Get all saved addresses (simple state management)
  Future<List<AddressModel>> getAllAddresses(
      {bool forceRefresh = false}) async {
    if (!isAuthenticated) return [];

    // If already loaded and not forcing refresh, return cached data
    if (_addressesLoaded && !forceRefresh) {
      LogMessage.p('Using cached addresses (${_addresses.length} addresses)');
      return _addresses;
    }

    try {
      LogMessage.p('Fetching addresses from Firestore');
      _addresses = await _firestore.getAllAddresses();
      _addressesLoaded = true;
      return _addresses;
    } catch (e) {
      LogMessage.p('Error fetching addresses: $e');
      return _addresses; // Return what we have, even if empty
    }
  }

  Future<AddressModel?> getSelectedAddressFromStorage() async {
    if (!isAuthenticated) return null;

    final addressId = AppPreferences.getSelectedAddressId();
    if (addressId != null) {
      final addresses = await getAllAddresses(forceRefresh: false);

      AddressModel? selected;
      for (final a in addresses) {
        if (a.id == addressId) {
          selected = a;
          break;
        }
      }

      if (selected != null) {
        LogMessage.p('Using stored address ID: $addressId');
        _currentSelectedAddress = selected;
        return selected;
      } else {
        LogMessage.p('Stored address ID not found in address list: $addressId');
      }
    } else {
      LogMessage.p('No stored address ID found');
    }

    return null;
  }

  Future<AddressModel?> getSelectedAddress() async {
    if (!isAuthenticated) return null;

    final storedAddress = await getSelectedAddressFromStorage();
    if (storedAddress != null) {
      return storedAddress;
    }

    if (_currentSelectedAddress != null && _lastKnownPosition != null) {
      final currentPosition = await _locationService.getCurrentPosition();
      if (currentPosition != null) {
        final distance = Geolocator.distanceBetween(
          _lastKnownPosition!.latitude,
          _lastKnownPosition!.longitude,
          currentPosition.latitude,
          currentPosition.longitude,
        );
        // If user hasn't moved more than 100m, return cached result
        if (distance < 100) {
          LogMessage.p(
              'Using cached selected address (moved ${distance.round()}m)');
          return _currentSelectedAddress;
        }
      }
    }

    try {
      // Get current location
      final currentPosition = await _locationService.getCurrentPosition();
      if (currentPosition == null) {
        // Fallback to first saved address if location unavailable
        final addresses = await getAllAddresses();
        return addresses.isNotEmpty ? addresses.first : null;
      }

      // Update last known position
      _lastKnownPosition = currentPosition;

      // Find nearest address within delivery radius
      final nearestAddress =
          await _findNearestAddressWithinRadius(currentPosition);

      if (nearestAddress != null) {
        // Found saved address within delivery radius
        _currentSelectedAddress = nearestAddress;
        return nearestAddress;
      }

      // No saved address within radius - return null
      // UI will show current location details and user can create address
      _currentSelectedAddress = null;
      return null;
    } catch (e) {
      LogMessage.p('Error getting selected address: $e');
      // Fallback to first saved address
      final addresses = await getAllAddresses();
      return addresses.isNotEmpty ? addresses.first : null;
    }
  }

  /// Manually refresh location and selected address (like Zepto's refresh button)
  /// Call this when user taps refresh or when returning from address screens
  Future<AddressModel?> refreshSelectedAddress() async {
    // Clear location cache to force fresh detection
    _lastKnownPosition = null;
    // Don't clear _currentSelectedAddress if user manually selected one

    // Get fresh selected address
    return await getSelectedAddress();
  }

  /// Force refresh addresses from server (call when needed)
  Future<List<AddressModel>> refreshAddresses() async {
    return await getAllAddresses(forceRefresh: true);
  }

  /// Save address to Firestore and update state
  Future<void> saveAddress(AddressModel address) async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated to save addresses');
    }

    try {
      await _firestore.saveAddress(address);

      // Update local state instead of refetching
      if (_addressesLoaded) {
        final existingIndex = _addresses.indexWhere((a) => a.id == address.id);
        if (existingIndex >= 0) {
          _addresses[existingIndex] = address; // Update existing
        } else {
          _addresses.add(address); // Add new
        }
        LogMessage.p('Updated local address state');
      }

      // Refresh selected address to potentially switch to new address if it's closer
      await getSelectedAddress();
    } catch (e) {
      LogMessage.p('Error saving address: $e');
      rethrow;
    }
  }

  /// Delete address from Firestore and update state
  Future<void> deleteAddress(String addressId) async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated to delete addresses');
    }

    try {
      await _firestore.deleteAddress(addressId);

      // Update local state instead of refetching
      if (_addressesLoaded) {
        _addresses.removeWhere((address) => address.id == addressId);
        LogMessage.p('Removed address from local state');
      }

      // Refresh selected address
      await getSelectedAddress();
    } catch (e) {
      LogMessage.p('Error deleting address: $e');
      rethrow;
    }
  }

  /// Find nearest saved address within delivery radius (500m)
  Future<AddressModel?> _findNearestAddressWithinRadius(
      Position currentPosition) async {
    final addresses = await getAllAddresses();
    if (addresses.isEmpty) return null;

    AddressModel? nearest;
    double minDistance = double.infinity;

    for (final address in addresses) {
      if (address.latitude != null && address.longitude != null) {
        final distance = Geolocator.distanceBetween(
          currentPosition.latitude,
          currentPosition.longitude,
          address.latitude!.toDouble(),
          address.longitude!.toDouble(),
        );

        // Only consider addresses within delivery radius
        if (distance <= _deliveryRadiusMeters && distance < minDistance) {
          minDistance = distance;
          nearest = address;
        }
      }
    }

    return nearest;
  }

  /// Get distance to selected address (for UI display)
  Future<double?> getDistanceToSelectedAddress() async {
    if (_currentSelectedAddress == null || _lastKnownPosition == null) {
      return null;
    }

    if (_currentSelectedAddress!.latitude != null &&
        _currentSelectedAddress!.longitude != null) {
      return Geolocator.distanceBetween(
        _lastKnownPosition!.latitude,
        _lastKnownPosition!.longitude,
        _currentSelectedAddress!.latitude!.toDouble(),
        _currentSelectedAddress!.longitude!.toDouble(),
      );
    }

    return null;
  }

  /// Check if user needs to go to settings for location permission
  Future<bool> shouldOpenAppSettings() async {
    final permission = await Geolocator.checkPermission();
    return permission == LocationPermission.deniedForever;
  }

  /// Open location settings
  Future<bool> openLocationSettings() async {
    try {
      return await Geolocator.openLocationSettings();
    } catch (e) {
      LogMessage.p('Error opening location settings: $e');
      return false;
    }
  }

  /// Open app settings
  Future<bool> openAppSettings() async {
    try {
      return await Geolocator.openAppSettings();
    } catch (e) {
      debugPrint('Error opening app settings: $e');
      return false;
    }
  }

  // Get current position
  Future<Position?> getCurrentPosition() async {
    try {
      // Check if we have permission first
      final hasPermission = await _locationService.checkLocationPermission();
      if (!hasPermission) {
        debugPrint('Location permission not granted');
        return null;
      }

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Location services are disabled');
        return null;
      }

      // Get current position with timeout and error handling
      // Try with high accuracy first, then fall back to medium if it fails
      try {
        return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
          timeLimit: const Duration(seconds: 10),
        );
      } catch (e) {
        debugPrint('High accuracy failed, trying medium accuracy: $e');
        return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.medium,
          timeLimit: const Duration(seconds: 15),
        );
      }
    } catch (e) {
      debugPrint('Error getting current position: $e');
      return null;
    }
  }

  // Get address from coordinates
  Future<List<Placemark>?> getAddressFromCoordinates(
      double latitude, double longitude) async {
    try {
      return await placemarkFromCoordinates(latitude, longitude);
    } catch (e) {
      debugPrint('Error getting address from coordinates: $e');
      return null;
    }
  }

  // Convert Placemark to AddressModel
  AddressModel placemarkToAddressModel(Placemark placemark, Position position,
      {String addressType = 'home', bool isDefault = false}) {
    final id = DateTime.now().millisecondsSinceEpoch.toString();

    final street = placemark.street ?? '';
    final subLocality = placemark.subLocality ?? '';
    final locality = placemark.locality ?? '';
    final administrativeArea = placemark.administrativeArea ?? '';
    final postalCode = placemark.postalCode ?? '';

    // Create address line 1 from street and subLocality
    final addressLine1 =
        [street, subLocality].where((e) => e.isNotEmpty).join(', ');

    // Create full address
    final fullAddress = [
      street,
      subLocality,
      locality,
      administrativeArea,
      postalCode,
    ].where((e) => e.isNotEmpty).join(', ');

    return AddressModel(
      id: id,
      fullAddress: fullAddress,
      addressLine1: addressLine1,
      city: locality,
      state: administrativeArea,
      pincode: postalCode,
      latitude: position.latitude,
      longitude: position.longitude,
      addressType: addressType,
      isDefault: isDefault,
    );
  }

  // Search for addresses based on query
  Future<List<Location>?> searchAddresses(String query) async {
    try {
      return await locationFromAddress(query);
    } catch (e) {
      debugPrint('Error searching addresses: $e');
      return null;
    }
  }

  // Get placemark from location
  Future<List<Placemark>?> getPlacemarkFromLocation(Location location) async {
    try {
      return await placemarkFromCoordinates(
          location.latitude, location.longitude);
    } catch (e) {
      debugPrint('Error getting placemark from location: $e');
      return null;
    }
  }

  /// Initialize address service (call this when user logs in)
  Future<void> initialize() async {
    if (isAuthenticated) {
      // Load addresses once on app start
      await getAllAddresses();
      // Get initial selected address (this will cache it for UI screens)
      await getSelectedAddress();
      LogMessage.p(
          'AddressService initialized with ${_addresses.length} addresses');
    }
  }

  /// Cleanup resources (call this when user logs out)
  void dispose() {
    _currentSelectedAddress = null;
    _lastKnownPosition = null;
    _addresses.clear();
    _addressesLoaded = false;
    _onAddressChanged = null; // Clear callback to prevent memory leaks
    LogMessage.p('AddressService disposed - all user data cleared');
  }

  /// Reset for new user session
  void reset() {
    dispose();
  }

  /// Get formatted distance string for UI
  String formatDistance(double distanceMeters) {
    if (distanceMeters < 1000) {
      return '${distanceMeters.round()}m away';
    } else {
      final km = distanceMeters / 1000;
      return '${km.toStringAsFixed(1)}km away';
    }
  }

  /// Get delivery radius in meters (for external use)
  static double get deliveryRadius => _deliveryRadiusMeters;

  Future<String> getEnhancedAddress(double latitude, double longitude) async {
    return await _googlePlacesService.getEnhancedAddress(latitude, longitude);
  }
}
